[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "models"
version = "0.1.0"
description = "A models management library for cloudseeder packages"
authors = [{ name = "GroGBot", email = "<EMAIL>" }]
license = { file = "LICENSE" }
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "pydantic",
]

[project.optional-dependencies]
dev = ["pytest", "black", "mypy"]

[tool.setuptools]
package-dir = {"" = "src"}

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-data]
models = ["*.json"]
