import React, { useState, useEffect, useRef } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>rid,
  Card,
  CardContent,
  CardHeader,
  Tabs,
  Tab,
  Chip,
  Divider,
  Button,
  alpha,
  Stack,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  SelectChangeEvent,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Paper,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Tooltip as MuiTooltip
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  Calendar,
  ChevronDown,
  ChevronRight
} from 'lucide-react';
import { Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, BarElement, Title, Tooltip, Legend, ArcElement } from 'chart.js';
import { Line } from 'react-chartjs-2';
import { apiClient } from '../apiClient';
import { useAuth } from '../context/AuthContext';
import { useParams, useNavigate } from 'react-router-dom';

// Register ChartJS components
ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, BarElement, Title, Tooltip, Legend, ArcElement);

// Define types for fetched data
interface AnsweredQuestion {
  question_code: string;
  question_text: string;
  answer_context: string[];
  answer_text: string[];
  answer_reason:string;
  confidence_score:number;
}

interface TranscriptionRating {
  id: string;
  request_id: string;
  question_code: string;
  answer_code: 'Correct' | 'Incorrect' | 'Hallucination';
  comment: string;
  admin_id: string;
}

interface CommentAnnotation {
  id: string;
  request_id: string;
  comment_idx: number;
  annotation: string;
  admin_id: string;
}

interface TranscriptionRun {
  id: string;
  user_id: string;
  visit_id: string;
  client_id: string;
  assessment_id: string;
  company_id: string;
  transcribe_type: string;
  audio_files: string[];
  question_files: string[];
  mode: string;
  status: string;
  started: number;
  completed: number;
  exception: string | null;
  answer_files: string[];
  transcription_files: string[];
  conversation_files: string[];
  total_questions: number;
  answered_questions_cnt: number;
  answered_questions: AnsweredQuestion[];
  unanswered_questions: AnsweredQuestion[];
  total_time: number;
  transcription_time: number;
  transcription_service_type: string;
  transcription_service_model: string;
  ai_service_type: string;
  ai_service_model: string;
  ai_temperature: number;
  ai_prompt: string | null;
  ai_embeddings: string[];
  audio_buffer_size: number;
  version: string;
  commit: string | null;
}

interface S3Url {
  data: string; // Full S3 URL 
}

// Helper to convert audio filename to S3 HTTPS URL using run variables
async function getS3Url(file: string, run: TranscriptionRun) {

    const data:S3Url = await apiClient(
                                              `file-path/${run.company_id}/${run.visit_id}/${run.assessment_id}/input/${file}`,
                                              { method: 'GET' },
                                              true
                                            );

                                            console.log("S3 URL data:", data);
    return data.data; // Assuming the API returns the full S3 URL directly
  // If the file is an audio file, return a signed S3 URL
  // return "https://codeskulptor-demos.commondatastorage.googleapis.com/GalaxyInvaders/alien_hit.wav";
  // Otherwise, construct the S3 HTTPS URL using run's company_id, visit_id, assessment_id
  // return `https://scribble2-data.s3.amazonaws.com/${run.company_id}/${run.visit_id}/${run.assessment_id}/input/${file}`;
}

// Fetch transcription runs from API
/**
 * ReportsPage component displays a list of transcription runs and provides detailed information.
 * 
 * URL-based navigation features:
 * - Direct linking to a specific transcription run via /reports/:transcription_request_id
 * - Automatic scrolling to the specified run when accessed via direct URL
 * - Ability to copy shareable links to specific transcription runs
 */
const ReportsPage = () => {
  const { user } = useAuth();
  const { transcription_request_id } = useParams<{ transcription_request_id?: string }>();
  const navigate = useNavigate();
  const [tabValue, setTabValue] = useState(0);
  const [timeRange, setTimeRange] = useState('30');
  // Use a more specific type for transcription runs if available, otherwise fallback to unknown[]
  const [newTranscriptionRuns, setNewTranscriptionRuns] = useState<TranscriptionRun[]>([]);
  const [jsonDataMap, setJsonDataMap] = useState<Record<string, unknown>>({});
  const [jsonLoadingMap, setJsonLoadingMap] = useState<Record<string, boolean>>({});
  // Create refs for transcription rows
  const transcriptionRefs = useRef<{ [key: string]: HTMLTableRowElement | null }>({});
  const [ratingsMap, setRatingsMap] = useState<Map<string, TranscriptionRating>>(new Map());
  const [commentAnnotationsMap, setCommentAnnotationsMap] = useState<Map<string, CommentAnnotation>>(new Map());
  const [annotationDialog, setAnnotationDialog] = useState<{
    open: boolean;
    run: TranscriptionRun | null;
    commentIdx: number | null;
    annotation: string;
    existing: CommentAnnotation | null;
  }>({
    open: false,
    run: null,
    commentIdx: null,
    annotation: '',
    existing: null,
  });
  // State for rating dialog
  const [ratingDialog, setRatingDialog] = useState<{
    open: boolean;
    type: 'Correct' | 'Incorrect' | 'Hallucination' | null;
    run: TranscriptionRun | null;
    question: AnsweredQuestion | null;
  }>({
    open: false,
    type: null,
    run: null,
    question: null,
  });
  const [ratingComment, setRatingComment] = useState('');

  // Add state for question tabs (per transcription run)
  const [questionTabsMap, setQuestionTabsMap] = useState<Record<string, number>>({});

  // Add state for audio URLs and loading status
  const [audioUrlMap, setAudioUrlMap] = useState<Record<string, string>>({});
  const [audioLoadingMap, setAudioLoadingMap] = useState<Record<string, boolean>>({});
  const [audioErrorMap, setAudioErrorMap] = useState<Record<string, string>>({});

  // Add state for audio refs
  const audioRefs = useRef<Record<string, HTMLAudioElement | null>>({});

  // Add state for expanded rows
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());

  // Tenant selector (company_id)
  const [selectedTenant, setSelectedTenant] = useState<string>(''); // '' = All Tenants
  const [tenantOptions, setTenantOptions] = useState<string[]>([]);

  // Set default customDateRange to last 7 days (end = today, start = 7 days ago)
  const today = new Date();
  const endDate = today.toISOString().slice(0, 10);
  const startDate = new Date(today.getTime() - 6 * 24 * 60 * 60 * 1000).toISOString().slice(0, 10);
  const [customDateRange, setCustomDateRange] = useState<{ start: string; end: string }>({ start: startDate, end: endDate });

  // Function to toggle row expansion
  const toggleRowExpansion = (runId: string) => {
    setExpandedRows(prev => {
      const newSet = new Set(prev);
      if (newSet.has(runId)) {
        newSet.delete(runId);
      } else {
        newSet.add(runId);
      }
      return newSet;
    });
  };

  // Function to scroll to a specific transcription
  const scrollToTranscription = (runId: string) => {
    const element = transcriptionRefs.current[runId];
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'start' }); // scroll to top
      // Add highlight effect
      element.style.backgroundColor = '#fff9c4';
      setTimeout(() => {
        element.style.transition = 'background-color 2s ease';
        element.style.backgroundColor = '';
      }, 100);
    }
  };

  // Extracted RatingDialog for performance
  const RatingDialog = React.memo(function RatingDialog({
    open, type, question, initialComment, onSubmit, onCancel, isEditing, adminId
  }: {
    open: boolean;
    type: 'Correct' | 'Incorrect' | 'Hallucination' | null;
    question: AnsweredQuestion | null;
    initialComment: string;
    onSubmit: (comment: string, type: 'Correct' | 'Incorrect' | 'Hallucination') => void;
    onCancel: () => void;
    isEditing: boolean;
    adminId?: string;
  }) {
    const [comment, setComment] = React.useState(initialComment);
    React.useEffect(() => {
      setComment(initialComment);
    }, [initialComment, open]);
    return (
      <Dialog open={open} onClose={onCancel} maxWidth="sm" fullWidth>
        <DialogTitle>
          {type && (
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Typography component="span">
                {isEditing ? `Edit Rating (${type})` : `Rate as ${type}`}
              </Typography>
              {isEditing && (
                <Chip
                  label={type}
                  size="small"
                  color={
                    type === 'Correct'
                      ? 'success'
                      : type === 'Incorrect'
                        ? 'warning'
                        : 'error'
                  }
                />
              )}
            </Box>
          )}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 1 }}>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              {question && `${question.question_code}: ${question.question_text}`}
            </Typography>
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" sx={{ mb: 1 }}>Rating Type</Typography>
              <Box sx={{ display: 'flex', gap: 2 }}>
                <Button
                  variant={type === 'Correct' ? 'contained' : 'outlined'}
                  color="success"
                  size="small"
                  onClick={() => onSubmit(comment, 'Correct')}
                  sx={{ minWidth: '100px' }}
                >
                  Correct
                </Button>
                <Button
                  variant={type === 'Incorrect' ? 'contained' : 'outlined'}
                  color="warning"
                  size="small"
                  onClick={() => onSubmit(comment, 'Incorrect')}
                  sx={{ minWidth: '100px' }}
                >
                  Incorrect
                </Button>
                <Button
                  variant={type === 'Hallucination' ? 'contained' : 'outlined'}
                  color="error"
                  size="small"
                  onClick={() => onSubmit(comment, 'Hallucination')}
                  sx={{ minWidth: '100px' }}
                >
                  Hallucination
                </Button>
              </Box>
            </Box>
            <TextField
              autoFocus
              margin="dense"
              label="Comment (optional)"
              type="text"
              fullWidth
              multiline
              rows={3}
              variant="outlined"
              value={comment}
              onChange={e => setComment(e.target.value)}
              placeholder="Add any additional notes or comments..."
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={onCancel}>Cancel</Button>
          <Button
            onClick={() => onSubmit(comment, type!)}
            variant="contained"
            color={
              type === 'Correct'
                ? 'success'
                : type === 'Incorrect'
                  ? 'warning'
                  : 'error'
            }
          >
            {isEditing ? 'Update Rating' : `Submit ${type}`}
          </Button>
        </DialogActions>
        {isEditing && adminId && (
          <Box sx={{ px: 3, pb: 1 }}>
            <Typography variant="caption" color="text.secondary">by {adminId}</Typography>
          </Box>
        )}
      </Dialog>
    ); // <-- This closes the return statement
  }); // <-- This closes the React.memo call

  const handleOpenAnnotationDialog = (run: TranscriptionRun, commentIdx: number, existing: CommentAnnotation | null) => {
    setAnnotationDialog({
      open: true,
      run,
      commentIdx,
      annotation: existing?.annotation || '',
      existing,
    });
  };

  const handleCloseAnnotationDialog = () => {
    setAnnotationDialog({ open: false, run: null, commentIdx: null, annotation: '', existing: null });
  };

  const handleSubmitAnnotation = async () => {
    if (!annotationDialog.run || annotationDialog.commentIdx == null) return;
    const id = `${annotationDialog.run.id}|${annotationDialog.commentIdx}`;
    const payload: CommentAnnotation = {
      id,
      request_id: annotationDialog.run.id,
      comment_idx: annotationDialog.commentIdx,
      annotation: annotationDialog.annotation,
      admin_id: user?.email || user?.id || '<EMAIL>',
    };
    try {
      await apiClient('comment-annotations', {
        method: 'POST',
        body: JSON.stringify(payload),
        headers: { 'Content-Type': 'application/json' },
      }, true);
      setCommentAnnotationsMap(prev => new Map(prev.set(id, payload)));
      handleCloseAnnotationDialog();
    } catch {
      // Optionally show error
    }
  };

  // Calculate accuracy rate (average score across all runs)
  const accuracyRate = newTranscriptionRuns.length
    ? Math.round(
      newTranscriptionRuns.reduce(
        (sum, run) =>
          sum + (run.total_questions ? (run.answered_questions_cnt / run.total_questions) * 100 : 0),
        0
      ) / newTranscriptionRuns.length
    )
    : 0;

  // Calculate accuracy based on correct answers from rated questions
  const accuracyMetric = React.useMemo(() => {
    let correctCount = 0;
    let totalCount = 0;
    ratingsMap.forEach(rating => {
      totalCount++;
      if (rating.answer_code === 'Correct') {
        correctCount++;
      }
    });
    const percentage = totalCount > 0 ? Math.round((correctCount / totalCount) * 100) : 0;
    return { percentage, correctCount, totalCount };
  }, [ratingsMap]);

  // Calculate hallucination percentage from rated questions
  const hallucinationMetric = React.useMemo(() => {
    let hallucinationCount = 0;
    let totalCount = 0;
    ratingsMap.forEach(rating => {
      totalCount++;
      if (rating.answer_code === 'Hallucination') {
        hallucinationCount++;
      }
    });
    const percentage = totalCount > 0 ? Math.round((hallucinationCount / totalCount) * 100) : 0;
    return { percentage, hallucinationCount, totalCount };
  }, [ratingsMap]);

  // Calculate incorrect percentage from rated questions
  const incorrectMetric = React.useMemo(() => {
    let incorrectCount = 0;
    let totalCount = 0;
    ratingsMap.forEach(rating => {
      totalCount++;
      if (rating.answer_code === 'Incorrect') {
        incorrectCount++;
      }
    });
    const percentage = totalCount > 0 ? Math.round((incorrectCount / totalCount) * 100) : 0;
    return { percentage, incorrectCount, totalCount };
  }, [ratingsMap]);

  // Sort newTranscriptionRuns by 'started' descending
  const sortedRuns = React.useMemo(() =>
    [...newTranscriptionRuns].sort((a, b) => b.started - a.started),
    [newTranscriptionRuns]
  );

  // Memoize chart data and options for performance
  const chartData = React.useMemo(() => ({
    labels: sortedRuns.map(run => new Date(run.started * 1000).toLocaleString()),
    datasets: [
      {
        label: 'Total Questions',
        data: sortedRuns.map(run => run.total_questions),
        borderColor: '#6366F1',
        backgroundColor: 'rgba(99, 102, 241, 0.5)',
        tension: 0.3,
      },
      {
        label: 'Answered Questions',
        data: sortedRuns.map(run => run.answered_questions_cnt),
        borderColor: '#0EA5E9',
        backgroundColor: 'rgba(14, 165, 233, 0.5)',
        tension: 0.3,
      },
      {
        label: 'Correct Questions',
        data: sortedRuns.map(() => 0),
        borderColor: '#10B981',
        backgroundColor: 'rgba(16, 185, 129, 0.5)',
        tension: 0.3,
      },
      {
        label: 'Hallucinations',
        data: sortedRuns.map(() => 0),
        borderColor: '#EF4444',
        backgroundColor: 'rgba(239, 68, 68, 0.5)',
        tension: 0.3,
      },
    ]
  }), [sortedRuns]);

  const chartOptions = React.useMemo(() => ({
    responsive: true,
    maintainAspectRatio: false,
    animation: { duration: 0 },
    plugins: {
      legend: {
        position: 'bottom' as const,
      },
    },
  }), []);

  // Function to copy a transcription link to clipboard
  const copyTranscriptionLink = (runId: string) => {
    const url = `${window.location.origin}/reports/${runId}`;
    navigator.clipboard.writeText(url)
      .then(() => {
        alert('Link copied to clipboard');
      })
      .catch(err => {
        console.error('Failed to copy link: ', err);
      });
  };

  // Handler for per-question correct/hallucination/incorrect - opens dialog
  const handleProcessQuestion = (
    type: 'Correct' | 'Incorrect' | 'Hallucination',
    run: TranscriptionRun,
    question: AnsweredQuestion
  ) => {
    setRatingDialog({
      open: true,
      type,
      run,
      question,
    });
    setRatingComment('');
  };

  // Handler for editing an existing rating
  const handleEditRating = (
    run: TranscriptionRun,
    question: AnsweredQuestion,
    existingRating: TranscriptionRating
  ) => {
    setRatingDialog({
      open: true,
      type: existingRating.answer_code,
      run,
      question,
    });
    setRatingComment(existingRating.comment || '');
  };

  // Handler for canceling the rating dialog
  const handleCancelRating = () => {
    setRatingDialog({ open: false, type: null, run: null, question: null });
    setRatingComment('');
  };

  const handleTimeRangeChange = (event: SelectChangeEvent<string>) => {
    const value = event.target.value;
    setTimeRange(value);
  };

  const handleTabChange = (_: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Function to handle question tab change
  const handleQuestionTabChange = (runId: string, newValue: number) => {
    setQuestionTabsMap(prev => ({ ...prev, [runId]: newValue }));
  };

  // Fetch transcription runs and ratings from API on mount and when timeRange or selectedTenant changes
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch transcription runs
        const runs = await apiClient<TranscriptionRun[]>(
          'transcription-request-list',
          { method: 'GET' },
          true
        );

        // Build unique tenant options from fetched runs
        const uniqueTenants = Array.from(new Set(
          runs.map(r => (r.company_id || '').trim()).filter(Boolean)
        )).sort();
        setTenantOptions(uniqueTenants);
        // If current selection is no longer available, reset to All Tenants
        if (selectedTenant && !uniqueTenants.map(t => t.toLowerCase()).includes(selectedTenant.toLowerCase())) {
          setSelectedTenant('');
        }

        let working = runs;
        // Remove time range filter
        // Filter by tenant (company_id) if selected
        if (selectedTenant) {
          const key = selectedTenant.toLowerCase();
          working = working.filter(r => (r.company_id || '').toLowerCase() === key);
        }
        // Filter by custom date range (required)
        const startEpoch = Math.floor(new Date(customDateRange.start).getTime() / 1000);
        const endEpoch = Math.floor(new Date(customDateRange.end).getTime() / 1000) + 86399; // end of day
        working = working.filter(r => typeof r.started === 'number' && r.started >= startEpoch && r.started <= endEpoch);
        setNewTranscriptionRuns(working);

        // Fetch existing ratings
        const ratings = await apiClient<TranscriptionRating[]>(
          'transcription-rating-list',
          { method: 'GET' },
          true
        );
        // Create a map for quick lookup by rating ID (run.id + '-' + question.question_code)
        const ratingsMapObj = new Map<string, TranscriptionRating>();
        ratings.forEach(rating => {
          ratingsMapObj.set(rating.id, rating);
        });
        setRatingsMap(ratingsMapObj);
      } catch {
        // Optionally show error
        setNewTranscriptionRuns([]);
        setRatingsMap(new Map());
      }
    };
    fetchData();
  }, [timeRange, selectedTenant, customDateRange]);

  // Fetch CommentAnnotations on mount
  useEffect(() => {
    const fetchAnnotations = async () => {
      try {
        const annotations = await apiClient<CommentAnnotation[]>(
          'comment-annotations-list',
          { method: 'GET' },
          true
        );
        const map = new Map<string, CommentAnnotation>();
        annotations.forEach(a => {
          map.set(`${a.request_id}|${a.comment_idx}`, a);
        });
        setCommentAnnotationsMap(map);
      } catch {
        setCommentAnnotationsMap(new Map());
      }
    };
    fetchAnnotations();
  }, []);

  // If a transcription_request_id is present in the URL, scroll to that report after data loads
  useEffect(() => {
    if (transcription_request_id && newTranscriptionRuns.length > 0) {
      // Use a timeout to ensure DOM is rendered
      setTimeout(() => {
        scrollToTranscription(transcription_request_id);
      }, 200);
    }
  }, [transcription_request_id, newTranscriptionRuns]);

  // Summary card component
  const StatCard = ({ title, value, change, subtitle, icon }: {
    title: string;
    value: string;
    change: number;
    subtitle: string;
    icon?: React.ReactNode;
  }) => (
    <Card sx={{ height: '100%' }}>
      <CardContent sx={{ p: 2.5 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
          <Typography variant="subtitle2" color="text.secondary">
            {title}
          </Typography>
          {icon && (
            <Box
              sx={{
                p: 1,
                borderRadius: 1,
                backgroundColor: alpha('#6366F1', 0.1),
                color: 'primary.main'
              }}
            >
              {icon}
            </Box>
          )}
        </Box>
        <Typography variant="h4" component="div" sx={{ mb: 0.5, fontWeight: 600 }}>
          {value}
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Chip
            icon={change >= 0 ? <TrendingUp size={14} /> : <TrendingDown size={14} />}
            label={`${Math.abs(change)}%`}
            size="small"
            color={change >= 0 ? 'success' : 'error'}
            sx={{
              height: 22,
              '& .MuiChip-label': { px: 1 },
              backgroundColor: change >= 0 ? alpha('#10B981', 0.1) : alpha('#EF4444', 0.1),
              color: change >= 0 ? 'success.dark' : 'error.dark',
              fontWeight: 500,
            }}
          />
          <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
            {subtitle}
          </Typography>
        </Box>
      </CardContent>
    </Card> 
  );

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom sx={{ fontWeight: 700 }}>
            AI Reports
          </Typography>
          <Typography variant="body1" color="text.secondary">
            View and analyze AI insights from user trascriptions
          </Typography>
        </Box>
        <Stack direction="row" spacing={2}>
          {/* Tenant (company_id) selector */}
          <FormControl size="small" sx={{ minWidth: 180 }}>
            <InputLabel id="tenant-select-label">tenant id</InputLabel>
            <Select
              labelId="tenant-select-label"
              id="tenant-select"
              value={selectedTenant}
              label="tenant id"
              onChange={(e: SelectChangeEvent<string>) => setSelectedTenant(e.target.value)}
            >
              <MenuItem value="">All Tenants</MenuItem>
              {tenantOptions.map(id => (
                <MenuItem key={id} value={id}>{id}</MenuItem>
              ))}
            </Select>
          </FormControl>
          {/* Custom Date Range Picker (required) */}
          <Box sx={{ minWidth: 260, display: 'flex', alignItems: 'center', gap: 1 }}>
            <TextField
              label="Start Date"
              type="date"
              size="small"
              InputLabelProps={{ shrink: true }}
              value={customDateRange.start}
              onChange={e => setCustomDateRange(r => ({ ...r, start: e.target.value }))}
              sx={{ minWidth: 120 }}
              inputProps={{ max: customDateRange.end }}
            />
            <TextField
              label="End Date"
              type="date"
              size="small"
              InputLabelProps={{ shrink: true }}
              value={customDateRange.end}
              onChange={e => setCustomDateRange(r => ({ ...r, end: e.target.value }))}
              sx={{ minWidth: 120 }}
              inputProps={{ min: customDateRange.start, max: endDate }}
            />
          </Box>
        </Stack>
      </Box>

      {/* Summary Stats */}
      <Grid container spacing={2} sx={{ mb: 1, flexWrap: 'nowrap' }}>
        <Grid item xs={12} sm={6} md={1.5} lg={1.5} xl={1.5} zeroMinWidth>
          <StatCard
            title="Total Transcriptions"
            value={String(newTranscriptionRuns.length)}
            change={5.3}
            subtitle="vs last month"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={1.5} lg={1.5} xl={1.5} zeroMinWidth>
          <StatCard
            title="Total Questions"
            value={String(newTranscriptionRuns.reduce((sum, run: TranscriptionRun) => sum + (run.total_questions ?? 0), 0))}
            change={8.1}
            subtitle="vs last month"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={1.5} lg={1.5} xl={1.5} zeroMinWidth>
          <StatCard
            title="Total Answered"
            value={String(newTranscriptionRuns.reduce((sum, run: TranscriptionRun) => sum + (run.answered_questions_cnt ?? 0), 0))}
            change={0}
            subtitle="vs last month"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={1.5} lg={1.5} xl={1.5} zeroMinWidth>
          <StatCard
            title="Response Time"
            value={(() => {
              if (newTranscriptionRuns.length === 0) return '0s';
              const avg = Math.round(
                newTranscriptionRuns.reduce((sum, run: TranscriptionRun) => sum + ((run.completed ?? 0) - (run.started ?? 0)), 0) / newTranscriptionRuns.length
              );
              return avg + 's';
            })()}
            change={-12.4}
            subtitle="vs last month"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={1.5} lg={1.5} xl={1.5} zeroMinWidth>
          <StatCard
            title="Answer Rate"
            value={`${accuracyRate}%`}
            change={2.7}
            subtitle="vs last month"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={1.5} lg={1.5} xl={1.5} zeroMinWidth>
          <StatCard
            title="Accuracy"
            value={`${accuracyMetric.percentage}%`}
            change={0}
            subtitle={`${accuracyMetric.correctCount}/${accuracyMetric.totalCount} rated`}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={1.5} lg={1.5} xl={1.5} zeroMinWidth>
          <StatCard
            title="Hallucinations"
            value={`${hallucinationMetric.percentage}%`}
            change={0}
            subtitle={`${hallucinationMetric.hallucinationCount}/${hallucinationMetric.totalCount} rated`}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={1.5} lg={1.5} xl={1.5} zeroMinWidth>
          <StatCard
            title="Incorrect"
            value={`${incorrectMetric.percentage}%`}
            change={0}
            subtitle={`${incorrectMetric.incorrectCount}/${incorrectMetric.totalCount} rated`}
          />
        </Grid>
      </Grid>

      {/* Tabs */}
      <Box sx={{ mb: 1 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          TabIndicatorProps={{
            style: { height: 3, borderRadius: 3 }
          }}
          sx={{
            minHeight: 32,
            '& .MuiTab-root': {
              minHeight: 32,
              py: 0.5,
              fontWeight: 600,
              fontSize: '0.85rem',
              textTransform: 'none',
              '&.Mui-selected': {
                color: 'primary.main',
              },
            },
          }}
        >
          <Tab label="Overview" />
          <Tab label="Sentiment" />
          <Tab label="Topics" />
          <Tab label="Users" />
        </Tabs>
        <Divider />
      </Box>

      {/* Charts */}
      <Grid container spacing={2}>
        {tabValue === 0 && (
          <>
            <Grid item xs={12}>
              <Card sx={{ height: 280 }}>
                <CardHeader
                  title="AI Metrics"
                  subheader="Transcription run stats"
                  titleTypographyProps={{ variant: 'h6', fontWeight: 600 }}
                  subheaderTypographyProps={{ variant: 'body2' }}
                  sx={{ py: 1.5 }}
                />
                <Divider />
                <CardContent sx={{ height: 180, p: 0, pl: 1, pr: 1, pb: 0 }}>
                  <Box sx={{ width: '100%' }}>
                    <Line
                      data={chartData}
                      options={chartOptions}
                    />
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </>
        )}

        {tabValue === 1 && (
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6">Sentiment Analysis</Typography>
                <Typography variant="body2" color="text.secondary">
                  This tab would display detailed sentiment analysis.
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        )}

        {tabValue === 2 && (
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6">Topic Analysis</Typography>
                <Typography variant="body2" color="text.secondary">
                  This tab would display detailed topic analysis.
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        )}

        {tabValue === 3 && (
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6">User Analysis</Typography>
                <Typography variant="body2" color="text.secondary">
                  This tab would display detailed user analysis.
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        )}
      </Grid>

      {/* Transcription Runs Table */}
      <Box sx={{ mt: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
          <Typography variant="h5" sx={{ fontWeight: 700 }}>
            Transcription Runs
          </Typography>
          {transcription_request_id && (
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                bgcolor: 'background.paper',
                p: 1,
                borderRadius: 1,
                border: '1px solid',
                borderColor: 'divider'
              }}
            >
              <Typography variant="body2" color="text.secondary" sx={{ mr: 1 }}>
                Viewing transcription: <strong>{transcription_request_id.substring(0, 8)}...</strong>
              </Typography>
              <Button
                size="small"
                variant="outlined"
                onClick={() => {
                  // Clear the specific transcription view and go back to list
                  // Use React Router's useNavigate for navigation and immediate filtering
                  navigate('/reports');
                }}
              >
                Clear
              </Button>
            </Box>
          )}
        </Box>
        <TableContainer component={Paper} variant="outlined" sx={{ mb: 0 }}>
          <Table size="small">
            <TableHead>
              <TableRow>
                <TableCell sx={{ verticalAlign: 'top', width: '40px' }}></TableCell>
                <TableCell sx={{ verticalAlign: 'top', width: '50%' }}>Transcription Summary</TableCell>
                <TableCell sx={{ verticalAlign: 'top', width: '50%' }}>Metrics</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {(transcription_request_id
                ? sortedRuns.filter(run => run.id === transcription_request_id)
                : sortedRuns
              ).map((run) => {
                // Calculate subtotals for this run
                const totalQuestions = run.total_questions || 0;
                const answeredQuestions = run.answered_questions_cnt || 0;
                let correct = 0, incorrect = 0, hallucination = 0, rated = 0;
                if (run.answered_questions && run.answered_questions.length > 0) {
                  run.answered_questions.forEach((q) => {
                    const ratingKey = run.id + '-' + q.question_code;
                    const rating = ratingsMap.get(ratingKey);
                    if (rating) {
                      rated++;
                      if (rating.answer_code === 'Correct') correct++;
                      else if (rating.answer_code === 'Incorrect') incorrect++;
                      else if (rating.answer_code === 'Hallucination') hallucination++;
                    }
                  });
                }
                const accuracy = rated ? (correct / rated) * 100 : 0;
                const hallucinationRate = rated ? (hallucination / rated) * 100 : 0;
                const incorrectRate = rated ? (incorrect / rated) * 100 : 0;
                const isExpanded = expandedRows.has(run.id);
                return (
                  <React.Fragment key={run.id + '-' + run.started}>
                    {/* Summary Row */}
                    <TableRow
                      key={run.id + '-summary'}
                      id={`transcription-${run.id}`}
                      ref={el => transcriptionRefs.current[run.id] = el}
                      sx={{
                        cursor: 'pointer',
                        transition: 'all 0.2s ease-in-out',
                        '&:hover': {
                          backgroundColor: '#f8f9fa',
                          transform: 'scale(1.002)',
                          boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                        },
                        ...(isExpanded && {
                          backgroundColor: '#e3f2fd',
                          borderLeft: '4px solid #2196f3'
                        })
                      }}
                      onClick={() => toggleRowExpansion(run.id)}
                    >
                      <TableCell sx={{ verticalAlign: 'top', width: '40px' }}>
                        <Box sx={{
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          width: 32,
                          height: 32,
                          borderRadius: '50%',
                          backgroundColor: isExpanded ? 'primary.main' : 'grey.200',
                          color: isExpanded ? 'white' : 'grey.600',
                          transition: 'all 0.2s ease-in-out',
                          '&:hover': {
                            backgroundColor: isExpanded ? 'primary.dark' : 'grey.300',
                            transform: 'scale(1.1)'
                          }
                        }}>
                          {isExpanded ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
                        </Box>
                      </TableCell>
                      <TableCell sx={{ verticalAlign: 'top' }}>
                        <Typography variant="h6" sx={{
                          fontWeight: 600,
                          mb: 1,
                          color: 'text.primary',
                          fontSize: '1.0rem',
                          lineHeight: 1.3
                        }}>
                          {new Date(run.started * 1000).toLocaleDateString()} {new Date(run.started * 1000).toLocaleTimeString()} - {run.user_id} visit {run.client_id} (#{run.visit_id})
                        </Typography>
                        <Box sx={{ display: 'flex', gap: 0.3, flexWrap: 'wrap' }}>
                          <Chip
                            label={`${run.ai_service_model}`}
                            size="small"
                            variant="outlined"
                            sx={{
                              backgroundColor: 'rgba(63, 81, 181, 0.08)',
                              borderColor: 'rgba(63, 81, 181, 0.3)',
                              color: 'indigo.main',
                              fontWeight: 500,
                              fontSize: '0.7rem',
                              height: '24px'
                            }}
                          />
                          <Chip
                            label={`${run.transcription_service_model}`}
                            size="small"
                            variant="outlined"
                            sx={{
                              backgroundColor: 'rgba(76, 175, 80, 0.08)',
                              borderColor: 'rgba(76, 175, 80, 0.3)',
                              color: 'success.main',
                              fontWeight: 500,
                              fontSize: '0.7rem',
                              height: '24px'
                            }}
                          />
                          <Chip
                            label={`${run.total_time}s`}
                            size="small"
                            variant="outlined"
                            sx={{
                              backgroundColor: 'rgba(255, 152, 0, 0.08)',
                              borderColor: 'rgba(255, 152, 0.3)',
                              color: 'warning.main',
                              fontWeight: 500,
                              fontSize: '0.7rem',
                              height: '24px'
                            }}
                          />
                        </Box>
                      </TableCell>
                      <TableCell sx={{ verticalAlign: 'top' }}>
                        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', alignItems: 'center' }}>
                          <MuiTooltip title={`Answered: ${answeredQuestions}/${totalQuestions} (${(answeredQuestions/totalQuestions*100).toFixed(1)}%)`}>
                            <Chip
                              label={`Answered: ${answeredQuestions}/${totalQuestions}`}
                              color="primary"
                              size="medium"
                              sx={{
                                fontWeight: 600,
                                minWidth: '120px',
                                fontSize: '0.85rem',
                                height: '32px'
                              }}
                            />
                          </MuiTooltip>
                          <MuiTooltip title={`Accuracy: ${accuracy.toFixed(1)}% (${correct} correct out of ${rated} rated)`}>
                            <Chip
                              label={`Accuracy: ${accuracy.toFixed(0)}%`}
                              color="success"
                              size="medium"
                              sx={{
                                fontWeight: 600,
                                minWidth: '100px',
                                fontSize: '0.85rem',
                                height: '32px'
                              }}
                            />
                          </MuiTooltip>
                          <MuiTooltip title={`Hallucinations: ${hallucinationRate.toFixed(1)}% (${hallucination} out of ${rated} rated)`}>
                            <Chip
                              label={`Hallucination: ${hallucinationRate.toFixed(0)}%`}
                              color="error"
                              size="medium"
                              sx={{
                                fontWeight: 600,
                                minWidth: '120px',
                                fontSize: '0.85rem',
                                height: '32px'
                              }}
                            />
                          </MuiTooltip>
                          <MuiTooltip title={`Incorrect: ${incorrectRate.toFixed(1)}% (${incorrect} out of ${rated} rated)`}>
                            <Chip
                              label={`Incorrect: ${incorrectRate.toFixed(0)}%`}
                              color="warning"
                              size="medium"
                              sx={{
                                fontWeight: 600,
                                minWidth: '100px',
                                fontSize: '0.85rem',
                                height: '32px'
                              }}
                            />
                          </MuiTooltip>
                        </Box>
                      </TableCell>
                    </TableRow>

                    {/* Detailed Content Row - Only shown when expanded */}
                    {isExpanded && (
                      <TableRow key={run.id + '-details'}>
                        <TableCell></TableCell>
                        <TableCell colSpan={2}>
                          <Box sx={{
                            p: 3,
                            backgroundColor: '#f8f9fa',
                            borderRadius: 2,
                            border: '1px solid #e9ecef',
                            animation: 'fadeIn 0.3s ease-in-out',
                            '@keyframes fadeIn': {
                              from: { opacity: 0, transform: 'translateY(-10px)' },
                              to: { opacity: 1, transform: 'translateY(0)' }
                            }
                          }}>
                            {/* Basic Info Section */}
                            <Grid container spacing={3}>
                              <Grid item xs={12} md={4}>
                                <Typography variant="h6" sx={{
                                  fontWeight: 700,
                                  mb: 2,
                                  color: 'primary.main',
                                  borderBottom: '2px solid',
                                  borderColor: 'primary.main',
                                  pb: 0.5
                                }}>Basic Information</Typography>
                                <Typography variant="body2"><b>Date/Time:</b> {new Date(run.started * 1000).toLocaleString()}</Typography>
                                <Typography variant="body2"><b>Tenant:</b> {run.company_id}</Typography>
                                <Typography variant="body2"><b>User:</b> {run.user_id}</Typography>
                                <Typography variant="body2"><b>Client:</b> {run.client_id}</Typography>
                                <Typography variant="body2"><b>Visit:</b> {run.visit_id}</Typography>
                                <Typography variant="body2"><b>Assessment:</b> {run.assessment_id}</Typography>
                                <Typography variant="body2">
                                  <b>ID:</b> <a
                                    href={`/reports/${run.id}`}
                                    onClick={(e) => {
                                      e.preventDefault();
                                      if (e.ctrlKey || e.metaKey) {
                                        copyTranscriptionLink(run.id);
                                      } else {
                                        navigate(`/reports/${run.id}`);
                                      }
                                    }}
                                    style={{
                                      textDecoration: 'none',
                                      color: '#2563eb',
                                      cursor: 'pointer'
                                    }}
                                    title="Click to navigate, Ctrl+Click to copy link"
                                  >
                                    {run.id.substring(0, 8)}...
                                  </a>
                                </Typography>
                              </Grid>

                              <Grid item xs={12} md={4}>
                                <Typography variant="h6" sx={{
                                  fontWeight: 700,
                                  mb: 2,
                                  color: 'secondary.main',
                                  borderBottom: '2px solid',
                                  borderColor: 'secondary.main',
                                  pb: 0.5
                                }}>AI Configuration</Typography>
                                <Typography variant="body2"><b>Vendor:</b> {run.ai_service_type}</Typography>
                                <Typography variant="body2"><b>Model:</b> {run.ai_service_model}</Typography>
                                <Typography variant="body2"><b>Temperature:</b> {run.ai_temperature}</Typography>
                                <Typography variant="body2"><b>Version:</b> {run.version}</Typography>
                              </Grid>

                              <Grid item xs={12} md={4}>
                                <Typography variant="h6" sx={{
                                  fontWeight: 700,
                                  mb: 2,
                                  color: 'success.main',
                                  borderBottom: '2px solid',
                                  borderColor: 'success.main',
                                  pb: 0.5
                                }}>Input Details</Typography>
                                <Typography variant="body2"><b>Service:</b> {run.transcription_service_type}</Typography>
                                <Typography variant="body2"><b>Model:</b> {run.transcription_service_model}</Typography>
                                <Typography variant="body2"><b>Question File:</b> {run.question_files}</Typography>
                                <Typography variant="body2"><b>Size:</b> {Math.round(run.audio_buffer_size / 1024)}KB</Typography>
                              </Grid>
                            </Grid>

                            {/* Audio Section */}
                            {run.audio_files && run.audio_files.length > 0 && (
                              <Box sx={{ mt: 3 }}>
                                <Typography variant="h6" sx={{
                                  fontWeight: 700,
                                  mb: 2,
                                  color: 'warning.main',
                                  borderBottom: '2px solid',
                                  borderColor: 'warning.main',
                                  pb: 0.5
                                }}>Audio Files</Typography>
                                <Typography variant="body2" sx={{ mb: 2 }}><b>Files:</b> {run.audio_files.join(', ')}</Typography>
                                {run.audio_files.map((file, idx) => {
                                  const key = `${run.id}-${idx}`;
                                  const isShown = !!jsonDataMap[key];
                                  return (
                                    <Box key={file + idx} sx={{ mb: 2, p: 2, border: '1px solid #e0e0e0', borderRadius: 1 }}>
                                      <Typography variant="subtitle2" sx={{ mb: 1 }}>{file}</Typography>
                                      {/* Audio Player */}
                                      {audioUrlMap[key] ? (
                                        <audio
                                          ref={el => { audioRefs.current[key] = el; }}
                                          controls
                                          style={{ width: '100%', marginBottom: '8px' }}
                                          src={audioUrlMap[key]}
                                        >
                                          Your browser does not support the audio element.
                                        </audio>
                                      ) : audioLoadingMap[key] ? (
                                        <Typography variant="body2" color="primary" sx={{ mb: 1 }}>Loading audio...</Typography>
                                      ) : (
                                        <Button
                                          variant="outlined"
                                          size="small"
                                          sx={{ mb: 1 }}
                                          onClick={async () => {
                                            setAudioLoadingMap(prev => ({ ...prev, [key]: true }));
                                            setAudioErrorMap(prev => ({ ...prev, [key]: '' }));
                                            try {
                                              const url = await getS3Url(file, run);
                                              setAudioUrlMap(prev => ({ ...prev, [key]: url }));
                                            } catch {
                                              setAudioErrorMap(prev => ({ ...prev, [key]: 'Failed to load audio' }));
                                            } finally {
                                              setAudioLoadingMap(prev => ({ ...prev, [key]: false }));
                                            }
                                          }}
                                        >
                                          Load & Play Audio
                                        </Button>
                                      )}
                                      {audioErrorMap[key] && (
                                        <Typography variant="body2" color="error" sx={{ mb: 1 }}>{audioErrorMap[key]}</Typography>
                                      )}

                                      {/* Conversation Toggle */}
                                      <Button
                                        variant="outlined"
                                        size="small"
                                        onClick={async () => {
                                          if (isShown) {
                                            setJsonDataMap(prev => ({ ...prev, [key]: undefined }));
                                            return;
                                          }
                                          setJsonLoadingMap(prev => ({ ...prev, [key]: true }));
                                          try {
                                            const data = await apiClient(
                                              `file-path/${run.company_id}/${run.visit_id}/${run.assessment_id}/output/conversation.json`,
                                              { method: 'GET' },
                                              true
                                            );
                                            setJsonDataMap(prev => ({ ...prev, [key]: data }));
                                          } catch {
                                            console.error('Failed to load conversation');
                                            setJsonDataMap(prev => ({ ...prev, [key]: { error: 'Failed to load JSON' } }));
                                          } finally {
                                            setJsonLoadingMap(prev => ({ ...prev, [key]: false }));
                                          }
                                        }}
                                        disabled={jsonLoadingMap[key]}
                                      >
                                        {jsonLoadingMap[key]
                                          ? 'Loading...'
                                          : isShown
                                            ? 'Hide Conversation'
                                            : 'Show Conversation'}
                                      </Button>

                                      {/* Conversation Content */}
                                      {isShown && (
                                        <Box sx={{ mt: 2, p: 2, backgroundColor: '#f5f5f5', borderRadius: 1 }}>
                                          {Array.isArray((jsonDataMap[key] as { data?: Array<{ speaker_id: number; text: string }> }).data)
                                            ? ((jsonDataMap[key] as { data: Array<{ speaker_id: number; text: string }> }).data).map((item, i) => {
                                                const annotationKey = `${run.id}|${i}`;
                                                const annotation = commentAnnotationsMap.get(annotationKey);
                                                return (
                                                  <Box key={i} sx={{ mb: 1, p: 1, borderRadius: 1, background: '#ffffff' }}>
                                                    <Typography variant="body2" sx={{ fontWeight: 500 }}>
                                                      Speaker {item.speaker_id}: {item.text}
                                                    </Typography>
                                                    {annotation && (
                                                      <Typography variant="caption" color="primary" sx={{ ml: 1 }}>
                                                        Annotation: {annotation.annotation}
                                                      </Typography>
                                                    )}
                                                    <Button
                                                      variant="text"
                                                      size="small"
                                                      sx={{ ml: 1, fontSize: '0.85em', textTransform: 'none' }}
                                                      onClick={() => handleOpenAnnotationDialog(run, i, annotation ?? null)}
                                                    >
                                                      {annotation ? 'Edit Annotation' : 'Add Annotation'}
                                                    </Button>
                                                  </Box>
                                                );
                                              })
                                            : <Typography variant="body2" color="text.secondary">No conversation data</Typography>
                                          }
                                        </Box>
                                      )}
                                    </Box>
                                  );
                                })}
                              </Box>
                            )}

                            {/* Questions Section */}
                            <Box sx={{ mt: 3 }}>
                              <Typography variant="h6" sx={{
                                fontWeight: 700,
                                mb: 2,
                                color: 'info.main',
                                borderBottom: '2px solid',
                                borderColor: 'info.main',
                                pb: 0.5
                              }}>Questions & Responses</Typography>
                              <Typography variant="body2" sx={{ mb: 2 }}>
                                <b>Answered:</b> {run.answered_questions_cnt}/{run.total_questions} ({run.total_questions ? Math.round((run.answered_questions_cnt / run.total_questions) * 100) : 0}%)
                                &nbsp;&nbsp;<b>Time:</b> {run.total_time}s
                              </Typography>

                              {/* Question Tabs */}
                              <Box sx={{ mb: 2 }}>
                                <Tabs
                                  value={questionTabsMap[run.id] || 0}
                                  onChange={(_, newValue) => handleQuestionTabChange(run.id, newValue)}
                                  variant="fullWidth"
                                  sx={{
                                    minHeight: 32,
                                    '& .MuiTab-root': {
                                      minHeight: 32,
                                      py: 0.5,
                                      fontSize: '0.75rem',
                                      textTransform: 'none',
                                    },
                                  }}
                                >
                                  <Tab
                                    label={`Answered (${run.answered_questions?.length || 0})`}
                                    disabled={!run.answered_questions?.length}
                                  />
                                  <Tab
                                    label={`Unanswered (${run.unanswered_questions?.length || 0})`}
                                    disabled={!run.unanswered_questions?.length}
                                  />
                                </Tabs>
                              </Box>

                        {/* Answered Questions Tab */}
                        {(questionTabsMap[run.id] || 0) === 0 && run.answered_questions && run.answered_questions.length > 0 && (
                          <Box>
                            {run.answered_questions.map((q: AnsweredQuestion, qidx: number) => (
                              <Box key={q.question_code + '-answered-' + qidx} sx={{ mb: 2, p: 1, border: '1px solid #e0e0e0', borderRadius: 1 }}>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                                  <Typography variant="subtitle2" sx={{ fontWeight: 600, flex: 1 }}>
                                    {q.question_code}: {q.question_text}
                                  </Typography>
                                  {(() => {
                                    const ratingKey = run.id + '-' + q.question_code;
                                    const existingRating = ratingsMap.get(ratingKey);
                                    if (existingRating) {
                                      return (
                                        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end', gap: 0.5 }}>
                                          <Chip
                                            label={existingRating.answer_code}
                                            color={
                                              existingRating.answer_code === 'Correct'
                                                ? 'success'
                                                : existingRating.answer_code === 'Incorrect'
                                                  ? 'warning'
                                                  : 'error'
                                            }
                                            size="small"
                                            sx={{ cursor: 'pointer' }}
                                            onClick={() => handleEditRating(run, q, existingRating)}
                                          />
                                          {existingRating.comment && (
                                            <Typography
                                              variant="caption"
                                              sx={{
                                                fontStyle: 'italic',
                                                color: 'text.secondary',
                                                maxWidth: '200px',
                                                textAlign: 'right',
                                                fontSize: '0.7rem',
                                                backgroundColor: 'rgba(0,0,0,0.04)',
                                                padding: '2px 6px',
                                                borderRadius: '4px',
                                                border: '1px solid rgba(0,0,0,0.1)'
                                              }}
                                            >
                                              "{existingRating.comment}"
                                            </Typography>
                                          )}
                                        </Box>
                                      );
                                    } else {
                                      return (
                                        <Box sx={{ display: 'flex', gap: 0.5 }}>
                                          <Button
                                            size="small"
                                            variant="outlined"
                                            color="success"
                                            sx={{ minWidth: 'auto', px: 1, py: 0.25, fontSize: '0.7rem' }}
                                            onClick={() => handleProcessQuestion('Correct', run, q)}
                                          >
                                            ✓
                                          </Button>
                                          <Button
                                            size="small"
                                            variant="outlined"
                                            color="warning"
                                            sx={{ minWidth: 'auto', px: 1, py: 0.25, fontSize: '0.7rem' }}
                                            onClick={() => handleProcessQuestion('Incorrect', run, q)}
                                          >
                                            ✗
                                          </Button>
                                          <Button
                                            size="small"
                                            variant="outlined"
                                            color="error"
                                            sx={{ minWidth: 'auto', px: 1, py: 0.25, fontSize: '0.7rem' }}
                                            onClick={() => handleProcessQuestion('Hallucination', run, q)}
                                          >
                                            H
                                          </Button>
                                        </Box>
                                      );
                                    }
                                  })()}
                                </Box>
                                
                                {/* Answer Context */}
                                <Box sx={{ mb: 1, p: 1, backgroundColor: 'rgba(25, 118, 210, 0.08)', borderRadius: 1, border: '1px solid rgba(25, 118, 210, 0.12)' }}>
                                  <Typography variant="body2" sx={{ fontWeight: 600, color: 'primary.main', mb: 0.5, fontSize: '0.75rem' }}>
                                    CONTEXT
                                  </Typography>
                                  <Typography variant="body2" sx={{ fontSize: '0.85rem' }}>
                                    {q.answer_context?.join(', ') || 'No context provided'}
                                  </Typography>
                                </Box>

                                {/* Answer Reason */}
                                {q.answer_reason && (
                                  <Box sx={{ mb: 1, p: 1, backgroundColor: 'rgba(156, 39, 176, 0.08)', borderRadius: 1, border: '1px solid rgba(156, 39, 176, 0.12)' }}>
                                    <Typography variant="body2" sx={{ fontWeight: 600, color: 'secondary.main', mb: 0.5, fontSize: '0.75rem' }}>
                                      REASONING ({q.confidence_score})
                                    </Typography>
                                    <Typography variant="body2" sx={{ fontSize: '0.85rem' }}>
                                      {Array.isArray(q.answer_reason) ? q.answer_reason.join(', ') : q.answer_reason}
                                    </Typography>
                                  </Box>
                                )}
                                {/* Answer Text */}
                                <Box sx={{ mb: 1, p: 1, backgroundColor: 'rgba(46, 125, 50, 0.08)', borderRadius: 1, border: '1px solid rgba(46, 125, 50, 0.12)' }}>
                                  <Typography variant="body2" sx={{ fontWeight: 600, color: 'success.main', mb: 0.5, fontSize: '0.75rem' }}>
                                    ANSWER
                                  </Typography>
                                  <Typography variant="body2" sx={{ fontWeight: 500, fontSize: '0.85rem' }}>
                                    {q.answer_text?.join(', ') || 'No answer provided'}
                                  </Typography>
                                </Box>
                              </Box>
                            ))}
                          </Box>
                        )}

                        {/* Unanswered Questions Tab */}
                        {(questionTabsMap[run.id] || 0) === 1 && run.unanswered_questions && run.unanswered_questions.length > 0 && (
                          <Box>
                            {run.unanswered_questions.map((q: AnsweredQuestion, qidx: number) => (
                              <Box key={q.question_code + '-unanswered-' + qidx} sx={{ mb: 2, p: 1, border: '1px solid #ffeb3b', borderRadius: 1, bgcolor: '#fffde7' }}>
                                <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                                  {q.question_code}: {q.question_text}
                                </Typography>
                                
                                {/* Answer Context */}
                                <Box sx={{ mb: 1, p: 1, backgroundColor: 'rgba(25, 118, 210, 0.08)', borderRadius: 1, border: '1px solid rgba(25, 118, 210, 0.12)' }}>
                                  <Typography variant="body2" sx={{ fontWeight: 600, color: 'primary.main', mb: 0.5, fontSize: '0.75rem' }}>
                                    CONTEXT
                                  </Typography>
                                  <Typography variant="body2" sx={{ fontSize: '0.85rem' }}>
                                    {q.answer_context?.join(', ') || 'No context available'}
                                  </Typography>
                                </Box>

                                {/* Answer Reason */}
                                {q.answer_reason && (
                                  <Box sx={{ mb: 1, p: 1, backgroundColor: 'rgba(156, 39, 176, 0.08)', borderRadius: 1, border: '1px solid rgba(156, 39, 176, 0.12)' }}>
                                    <Typography variant="body2" sx={{ fontWeight: 600, color: 'secondary.main', mb: 0.5, fontSize: '0.75rem' }}>
                                      REASONING
                                    </Typography>
                                    <Typography variant="body2" sx={{ fontSize: '0.85rem' }}>
                                      {Array.isArray(q.answer_reason) ? q.answer_reason.join(', ') : q.answer_reason}
                                    </Typography>
                                  </Box>
                                )}
                                
                                {/* Status */}
                                <Chip label="Unanswered" color="warning" size="small" />
                              </Box>
                            ))}
                          </Box>
                        )}

                              {/* No questions message */}
                              {(!run.answered_questions?.length && !run.unanswered_questions?.length) && (
                                <Typography variant="body2" color="text.secondary">
                                  No questions available
                                </Typography>
                              )}
                            </Box>
                          </Box>
                        </TableCell>
                      </TableRow>
                    )}
                  </React.Fragment>
                );
              })}
            </TableBody>
          </Table>
        </TableContainer>
      </Box>

      {/* Rating Dialog */}
      <RatingDialog
        open={ratingDialog.open}
        type={ratingDialog.type}
        question={ratingDialog.question}
        initialComment={ratingComment}
        isEditing={(() => {
          const ratingKey = ratingDialog.run?.id + '-' + ratingDialog.question?.question_code;
          return Boolean(ratingKey && ratingsMap.has(ratingKey));
        })()}
        adminId={(() => {
          const ratingKey = ratingDialog.run?.id + '-' + ratingDialog.question?.question_code;
          return ratingKey && ratingsMap.get(ratingKey)?.admin_id;
        })()}
        onSubmit={async (comment, type) => {
          if (!type || !ratingDialog.run || !ratingDialog.question) return;
          try {
            const ratingId = ratingDialog.run.id + '-' + ratingDialog.question.question_code;
            const ratingPayload = {
              id: ratingId,
              request_id: ratingDialog.run.id,
              question_code: ratingDialog.question.question_code,
              answer_code: type,
              comment,
              admin_id: user?.email || user?.id || "<EMAIL>"
            };
            await apiClient('transcription-rating', {
              method: 'POST',
              body: JSON.stringify(ratingPayload),
              headers: { 'Content-Type': 'application/json' },
            }, true);
            setRatingsMap(prev => new Map(prev.set(ratingId, ratingPayload as TranscriptionRating)));
            setRatingDialog({ open: false, type: null, run: null, question: null });
            setRatingComment('');
          } catch {
            console.error('Failed to submit rating');
          }
        }}
        onCancel={handleCancelRating}
      />

      {/* Annotation Dialog */}
      <Dialog open={annotationDialog.open} onClose={handleCloseAnnotationDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {annotationDialog.existing ? 'Edit Annotation' : 'Add Annotation'}
        </DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Annotation"
            type="text"
            fullWidth
            multiline
            rows={3}
            variant="outlined"
            value={annotationDialog.annotation}
            onChange={e => setAnnotationDialog(prev => ({ ...prev, annotation: e.target.value }))}
            placeholder="Add your annotation for this dialog..."
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseAnnotationDialog}>Cancel</Button>
          <Button onClick={handleSubmitAnnotation} variant="contained" color="primary">
            {annotationDialog.existing ? 'Update' : 'Add'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ReportsPage;
