import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
    Box,
    Typography,
    Card,
    CardContent,
    Grid,
    Button,
    CircularProgress,
    Alert,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Paper,
    Chip,
    Divider,
    TextField,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    IconButton,
    Tooltip,
    SelectChangeEvent,
    Tabs,
    Tab
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import {
    Eye,
    Download,
    Filter,
    RefreshCw,
    FileText,
    FileSpreadsheet,
    BarChart3
} from 'lucide-react';
import { tenantReportsApi, TenantInfo, TenantReportResponse, VisitInfo } from '../apiClient';

interface TabPanelProps {
    children?: React.ReactNode;
    index: number;
    value: number;
}

function TabPanel(props: TabPanelProps) {
    const { children, value, index, ...other } = props;

    return (
        <div
            role="tabpanel"
            hidden={value !== index}
            id={`simple-tabpanel-${index}`}
            aria-labelledby={`simple-tab-${index}`}
            {...other}
        >
            {value === index && (
                <Box sx={{ p: 3 }}>
                    {children}
                </Box>
            )}
        </div>
    );
}

function a11yProps(index: number) {
    return {
        id: `simple-tab-${index}`,
        'aria-controls': `simple-tabpanel-${index}`,
    };
}

const VisitReportPage: React.FC = () => {
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [tenants, setTenants] = useState<TenantInfo[]>([]);
    const [selectedTenant, setSelectedTenant] = useState<string>('');
    const [tenantData, setTenantData] = useState<TenantReportResponse | null>(null);
    const [visitDetails, setVisitDetails] = useState<any | null>(null);
    const [showVisitDialog, setShowVisitDialog] = useState(false);
    const [tabValue, setTabValue] = useState(0);
    const [filters, setFilters] = useState({
        status: '',
        visit_type: '',
        start_date: '',
        end_date: ''
    });

    // Memoize filters to prevent infinite loops
    const memoizedFilters = useMemo(() => filters, [filters.status, filters.visit_type, filters.start_date, filters.end_date]);

    // Load tenants on component mount
    useEffect(() => {
        loadTenants();
    }, []);

    // Load tenant visits when tenant is selected
    useEffect(() => {
        if (selectedTenant) {
            loadTenantVisits();
        }
    }, [selectedTenant, memoizedFilters]);

    const loadTenants = useCallback(async () => {
        setLoading(true);
        setError(null);

        try {
            const data = await tenantReportsApi.getTenants();
            setTenants(data);
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to load tenants');
        } finally {
            setLoading(false);
        }
    }, []);

    const loadTenantVisits = useCallback(async () => {
        setLoading(true);
        setError(null);

        try {
            // Convert date formats for backend
            const startDate = filters.start_date ? new Date(filters.start_date).toISOString().split('T')[0] : undefined;
            const endDate = filters.end_date ? new Date(filters.end_date).toISOString().split('T')[0] : undefined;

            const data = await tenantReportsApi.getTenantVisits({
                tenant_id: selectedTenant,
                status: filters.status || undefined,
                visit_type: filters.visit_type || undefined,
                start_date: startDate,
                end_date: endDate,
                limit: 100,
                offset: 0
            });
            setTenantData(data);
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to load visits');
        } finally {
            setLoading(false);
        }
    }, [selectedTenant, filters]);

    const loadVisitDetails = useCallback(async (visitNo: string) => {
        try {
            const data = await tenantReportsApi.getVisitDetails(selectedTenant, visitNo);
            setVisitDetails(data);
            setShowVisitDialog(true);
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to load visit details');
        }
    }, [selectedTenant]);

    const handleTenantChange = useCallback((event: SelectChangeEvent<string>) => {
        setSelectedTenant(event.target.value);
    }, []);

    const handleFilterChange = useCallback((field: string, value: string) => {
        setFilters(prev => ({
            ...prev,
            [field]: value
        }));
    }, []);

    const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
        setTabValue(newValue);
    };

    const clearFilters = useCallback(() => {
        setFilters({
            status: '',
            visit_type: '',
            start_date: '',
            end_date: ''
        });
    }, []);

    const exportVisitsJSON = useCallback(async () => {
        try {
            const startDate = filters.start_date ? new Date(filters.start_date).toISOString().split('T')[0] : undefined;
            const endDate = filters.end_date ? new Date(filters.end_date).toISOString().split('T')[0] : undefined;

            const data = await tenantReportsApi.exportTenantData({
                tenant_id: selectedTenant,
                data_type: 'visits',
                format: 'json',
                start_date: startDate,
                end_date: endDate
            });

            if (data && data.data) {
                const blob = new Blob([JSON.stringify(data.data, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `visits_${selectedTenant}_${new Date().toISOString().split('T')[0]}.json`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            }
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to export visits');
        }
    }, [selectedTenant, filters]);

    const exportVisitsExcel = useCallback(async () => {
        try {
            const startDate = filters.start_date ? new Date(filters.start_date).toISOString().split('T')[0] : undefined;
            const endDate = filters.end_date ? new Date(filters.end_date).toISOString().split('T')[0] : undefined;

            const data = await tenantReportsApi.exportTenantData({
                tenant_id: selectedTenant,
                data_type: 'visits',
                format: 'csv',
                start_date: startDate,
                end_date: endDate
            });

            if (data && data.data) {
                // Convert JSON to CSV
                const headers = ['Tenant Name', 'Visit No', 'Visit Date', 'Visit Time', 'Visit Type', 'Visit Status', 'Client Name', 'Clinician Name'];
                const csvContent = [
                    headers.join(','),
                    ...data.data.map((visit: any) => [
                        visit.tenantName || '',
                        visit.visitNo || visit.no || '',
                        visit.visitDate ? formatDate(visit.visitDate) : '',
                        visit.visitStartTime || visit.startTime || '',
                        visit.visitType || visit.type || '',
                        visit.status || '',
                        visit.clientName || '',
                        visit.clinicianName || ''
                    ].join(','))
                ].join('\n');

                const blob = new Blob([csvContent], { type: 'text/csv' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `visits_${selectedTenant}_${new Date().toISOString().split('T')[0]}.csv`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            }
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to export visits');
        }
    }, [selectedTenant, filters]);

    const getStatusColor = (status?: string) => {
        switch (status) {
            case 'New':
                return 'primary';
            case 'In Progress':
                return 'warning';
            case 'Completed':
                return 'success';
            case 'Cancelled':
                return 'error';
            default:
                return 'default';
        }
    };

    const formatDate = (dateString: string) => {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            month: '2-digit',
            day: '2-digit',
            year: 'numeric'
        });
    };

    const formatTime = (timeString: string) => {
        return timeString || 'N/A';
    };

    return (
        <Box sx={{ p: 3 }}>
            <Typography variant="h4" gutterBottom>
                Visit Report
            </Typography>

            {error && (
                <Alert severity="error" sx={{ mb: 2 }}>
                    {error}
                </Alert>
            )}

            {/* Tenant Selection and Filters in Column Layout */}
            <Grid container spacing={3} sx={{ mb: 3 }}>
                {/* Tenant Selection */}
                <Grid item xs={12} md={4}>
                    <Card>
                        <CardContent>
                            <Typography variant="h6" gutterBottom>
                                Select Tenant
                            </Typography>
                            <FormControl fullWidth>
                                <InputLabel>Tenant</InputLabel>
                                <Select
                                    value={selectedTenant}
                                    onChange={handleTenantChange}
                                    label="Tenant"
                                >
                                    {tenants.map((tenant) => (
                                        <MenuItem key={tenant.tenantId} value={tenant.tenantId}>
                                            {tenant.tenantName}
                                        </MenuItem>
                                    ))}
                                </Select>
                            </FormControl>
                        </CardContent>
                    </Card>
                </Grid>

                {/* Filters */}
                <Grid item xs={12} md={8}>
                    <Card>
                        <CardContent>
                            <Typography variant="h6" gutterBottom>
                                <Filter size={20} />
                                Filters
                            </Typography>
                            <Grid container spacing={2}>
                                <Grid item xs={12} sm={6}>
                                    <FormControl fullWidth>
                                        <InputLabel>Status</InputLabel>
                                        <Select
                                            value={filters.status}
                                            onChange={(e) => handleFilterChange('status', e.target.value)}
                                            label="Status"
                                        >
                                            <MenuItem value="">All Statuses</MenuItem>
                                            <MenuItem value="New">New</MenuItem>
                                            <MenuItem value="Past Due">Past Due</MenuItem>
                                            <MenuItem value="Submitted for Processing">Submitted for Processing</MenuItem>
                                        </Select>
                                    </FormControl>
                                </Grid>
                                <Grid item xs={12} sm={6}>
                                    <FormControl fullWidth>
                                        <InputLabel>Visit Type</InputLabel>
                                        <Select
                                            value={filters.visit_type}
                                            onChange={(e) => handleFilterChange('visit_type', e.target.value)}
                                            label="Visit Type"
                                        >
                                            <MenuItem value="">All Types</MenuItem>
                                            <MenuItem value="Normal">Normal</MenuItem>
                                            <MenuItem value="SOC">SOC</MenuItem>
                                        </Select>
                                    </FormControl>
                                </Grid>
                                <Grid item xs={12} sm={6}>
                                    <LocalizationProvider dateAdapter={AdapterDateFns}>
                                        <DatePicker
                                            label="Start Date"
                                            value={filters.start_date ? new Date(filters.start_date) : null}
                                            onChange={(newValue) => {
                                                if (newValue) {
                                                    handleFilterChange('start_date', newValue.toISOString().split('T')[0]);
                                                } else {
                                                    handleFilterChange('start_date', '');
                                                }
                                            }}
                                            slotProps={{
                                                textField: {
                                                    fullWidth: true,
                                                    size: 'small'
                                                }
                                            }}
                                            format="MM/dd/yyyy"
                                        />
                                    </LocalizationProvider>
                                </Grid>
                                <Grid item xs={12} sm={6}>
                                    <LocalizationProvider dateAdapter={AdapterDateFns}>
                                        <DatePicker
                                            label="End Date"
                                            value={filters.end_date ? new Date(filters.end_date) : null}
                                            onChange={(newValue) => {
                                                if (newValue) {
                                                    handleFilterChange('end_date', newValue.toISOString().split('T')[0]);
                                                } else {
                                                    handleFilterChange('end_date', '');
                                                }
                                            }}
                                            slotProps={{
                                                textField: {
                                                    fullWidth: true,
                                                    size: 'small'
                                                }
                                            }}
                                            format="MM/dd/yyyy"
                                        />
                                    </LocalizationProvider>
                                </Grid>
                            </Grid>
                            <Box sx={{ mt: 2 }}>
                                <Button
                                    variant="outlined"
                                    onClick={clearFilters}
                                    sx={{ mr: 1 }}
                                >
                                    Clear Filters
                                </Button>
                                <Button
                                    variant="contained"
                                    onClick={loadTenantVisits}
                                    startIcon={<RefreshCw />}
                                >
                                    Refresh
                                </Button>
                            </Box>
                        </CardContent>
                    </Card>
                </Grid>
            </Grid>

            {selectedTenant && (
                <>
                    {/* Statistics */}
                    {tenantData && (
                        <Grid container spacing={3} sx={{ mb: 3 }}>
                            {/* Total Visits Card */}
                            <Grid item xs={12} sm={6} md={3}>
                                <Card>
                                    <CardContent>
                                        <Typography variant="h6" color="primary">
                                            {tenantData.total_visits}
                                        </Typography>
                                        <Typography variant="body2" color="text.secondary">
                                            Total Visits
                                        </Typography>
                                    </CardContent>
                                </Card>
                            </Grid>

                            {/* Dynamic Status Cards */}
                            {Object.entries(tenantData.visits_by_status).map(([status, count], index) => {
                                // Skip if count is 0
                                if (count === 0) return null;

                                // Define colors for different statuses
                                const getStatusColor = (status: string) => {
                                    switch (status) {
                                        case 'New':
                                            return 'info.main';
                                        case 'Past Due':
                                            return 'error.main';
                                        case 'Submitted for Processing':
                                            return 'warning.main';
                                        case 'Unknown':
                                            return 'grey.500';
                                        default:
                                            return 'text.secondary';
                                    }
                                };

                                return (
                                    <Grid item xs={12} sm={6} md={3} key={status}>
                                        <Card>
                                            <CardContent>
                                                <Typography variant="h6" color={getStatusColor(status)}>
                                                    {count}
                                                </Typography>
                                                <Typography variant="body2" color="text.secondary">
                                                    {status}
                                                </Typography>
                                            </CardContent>
                                        </Card>
                                    </Grid>
                                );
                            })}
                        </Grid>
                    )}

                    {/* Tabs Section */}
                    {tenantData && (
                        <Card>
                            <CardContent>
                                <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                                    <Tabs value={tabValue} onChange={handleTabChange} aria-label="tenant reports tabs">
                                        <Tab
                                            label="Visits"
                                            icon={<FileText size={16} />}
                                            iconPosition="start"
                                            {...a11yProps(0)}
                                        />
                                        <Tab
                                            label="Visits by Date"
                                            icon={<BarChart3 size={16} />}
                                            iconPosition="start"
                                            {...a11yProps(1)}
                                        />
                                    </Tabs>
                                </Box>

                                <TabPanel value={tabValue} index={0}>
                                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                                        <Typography variant="h6">
                                            Visits for {tenantData.tenant.tenantName}
                                        </Typography>
                                        <Box sx={{ display: 'flex', gap: 1 }}>
                                            <Button
                                                variant="outlined"
                                                startIcon={<FileText />}
                                                onClick={exportVisitsJSON}
                                                size="small"
                                                disabled={!tenantData.visits || tenantData.visits.length === 0}
                                            >
                                                Export JSON
                                            </Button>
                                            <Button
                                                variant="contained"
                                                startIcon={<FileSpreadsheet />}
                                                onClick={exportVisitsExcel}
                                                size="small"
                                                disabled={!tenantData.visits || tenantData.visits.length === 0}
                                            >
                                                Export Excel
                                            </Button>
                                        </Box>
                                    </Box>

                                    {loading ? (
                                        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                                            <CircularProgress />
                                        </Box>
                                    ) : (
                                        <TableContainer component={Paper}>
                                            <Table>
                                                <TableHead>
                                                    <TableRow>
                                                        <TableCell>Visit No</TableCell>
                                                        <TableCell>Visit Date</TableCell>
                                                        <TableCell>Visit Time</TableCell>
                                                        <TableCell>Visit Type</TableCell>
                                                        <TableCell>Visit Status</TableCell>
                                                        <TableCell>Actions</TableCell>
                                                    </TableRow>
                                                </TableHead>
                                                <TableBody>
                                                    {tenantData.visits.map((visit: VisitInfo) => (
                                                        <TableRow key={visit._id}>
                                                            <TableCell>{visit.visitNo}</TableCell>
                                                            <TableCell>{formatDate(visit.visitDate)}</TableCell>
                                                            <TableCell>{formatTime(visit.visitStartTime)}</TableCell>
                                                            <TableCell>{visit.visitType}</TableCell>
                                                            <TableCell>
                                                                <Chip
                                                                    label={visit.status || 'Unknown'}
                                                                    color={getStatusColor(visit.status) as any}
                                                                    size="small"
                                                                />
                                                            </TableCell>
                                                            <TableCell>
                                                                <Tooltip title="View Details">
                                                                    <IconButton
                                                                        size="small"
                                                                        onClick={() => loadVisitDetails(visit.visitNo)}
                                                                    >
                                                                        <Eye size={20} />
                                                                    </IconButton>
                                                                </Tooltip>
                                                            </TableCell>
                                                        </TableRow>
                                                    ))}
                                                </TableBody>
                                            </Table>
                                        </TableContainer>
                                    )}
                                </TabPanel>

                                <TabPanel value={tabValue} index={1}>
                                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                                        <Typography variant="h6">
                                            Visits by Date for {tenantData.tenant.tenantName}
                                        </Typography>
                                        <Box sx={{ display: 'flex', gap: 1 }}>
                                            <Button
                                                variant="outlined"
                                                startIcon={<FileText />}
                                                onClick={exportVisitsJSON}
                                                size="small"
                                                disabled={!tenantData.visits || tenantData.visits.length === 0}
                                            >
                                                Export JSON
                                            </Button>
                                            <Button
                                                variant="contained"
                                                startIcon={<FileSpreadsheet />}
                                                onClick={exportVisitsExcel}
                                                size="small"
                                                disabled={!tenantData.visits || tenantData.visits.length === 0}
                                            >
                                                Export Excel
                                            </Button>
                                        </Box>
                                    </Box>

                                    {loading ? (
                                        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                                            <CircularProgress />
                                        </Box>
                                    ) : (
                                        <TableContainer component={Paper}>
                                            <Table>
                                                <TableHead>
                                                    <TableRow>
                                                        <TableCell>Date</TableCell>
                                                        <TableCell>Total Visits</TableCell>
                                                        <TableCell>Visit Type</TableCell>
                                                        <TableCell>New</TableCell>
                                                        <TableCell>Past Due</TableCell>
                                                        <TableCell>Submitted for Processing</TableCell>
                                                        <TableCell>Unknown</TableCell>
                                                    </TableRow>
                                                </TableHead>
                                                <TableBody>
                                                    {(() => {
                                                        // Group visits by date
                                                        const visitsByDate = tenantData.visits.reduce((acc: any, visit: VisitInfo) => {
                                                            const date = formatDate(visit.visitDate);
                                                            if (!acc[date]) {
                                                                acc[date] = {
                                                                    total: 0,
                                                                    visitTypes: new Set(),
                                                                    New: 0,
                                                                    'Past Due': 0,
                                                                    'Submitted for Processing': 0,
                                                                    Unknown: 0
                                                                };
                                                            }
                                                            acc[date].total++;
                                                            acc[date].visitTypes.add(visit.visitType);
                                                            const status = visit.status || 'Unknown';
                                                            acc[date][status]++;
                                                            return acc;
                                                        }, {});

                                                        // Convert to array and sort by date
                                                        return Object.entries(visitsByDate)
                                                            .sort(([a], [b]) => new Date(b).getTime() - new Date(a).getTime())
                                                            .map(([date, stats]: [string, any]) => (
                                                                <TableRow key={date}>
                                                                    <TableCell>{date}</TableCell>
                                                                    <TableCell>{stats.total}</TableCell>
                                                                    <TableCell>{Array.from(stats.visitTypes).join(', ')}</TableCell>
                                                                    <TableCell>{stats.New}</TableCell>
                                                                    <TableCell>{stats['Past Due']}</TableCell>
                                                                    <TableCell>{stats['Submitted for Processing']}</TableCell>
                                                                    <TableCell>{stats.Unknown}</TableCell>
                                                                </TableRow>
                                                            ));
                                                    })()}
                                                </TableBody>
                                            </Table>
                                        </TableContainer>
                                    )}
                                </TabPanel>
                            </CardContent>
                        </Card>
                    )}
                </>
            )}

            {/* Visit Details Dialog */}
            <Dialog
                open={showVisitDialog}
                onClose={() => setShowVisitDialog(false)}
                maxWidth="md"
                fullWidth
            >
                <DialogTitle>
                    Visit Details
                </DialogTitle>
                <DialogContent>
                    {visitDetails && (
                        <Box>
                            <Typography variant="h6" gutterBottom>
                                Visit Information
                            </Typography>
                            <Grid container spacing={2}>
                                <Grid item xs={6}>
                                    <Typography variant="body2" color="text.secondary">
                                        Visit No
                                    </Typography>
                                    <Typography variant="body1">
                                        {visitDetails.visit?.visitNo || visitDetails.visit?.no}
                                    </Typography>
                                </Grid>
                                <Grid item xs={6}>
                                    <Typography variant="body2" color="text.secondary">
                                        Date
                                    </Typography>
                                    <Typography variant="body1">
                                        {visitDetails.visit?.visitDate ? formatDate(visitDetails.visit.visitDate) : 'N/A'}
                                    </Typography>
                                </Grid>
                                <Grid item xs={6}>
                                    <Typography variant="body2" color="text.secondary">
                                        Time
                                    </Typography>
                                    <Typography variant="body1">
                                        {visitDetails.visit?.visitStartTime || visitDetails.visit?.startTime || 'N/A'}
                                    </Typography>
                                </Grid>
                                <Grid item xs={6}>
                                    <Typography variant="body2" color="text.secondary">
                                        Type
                                    </Typography>
                                    <Typography variant="body1">
                                        {visitDetails.visit?.visitType || visitDetails.visit?.type || 'N/A'}
                                    </Typography>
                                </Grid>
                                <Grid item xs={6}>
                                    <Typography variant="body2" color="text.secondary">
                                        Status
                                    </Typography>
                                    <Chip
                                        label={visitDetails.visit?.status || 'Unknown'}
                                        color={getStatusColor(visitDetails.visit?.status || '') as any}
                                        size="small"
                                    />
                                </Grid>
                            </Grid>

                            {visitDetails.related_data?.client && (
                                <>
                                    <Divider sx={{ my: 2 }} />
                                    <Typography variant="h6" gutterBottom>
                                        Client Information
                                    </Typography>
                                    <Typography variant="body1">
                                        Client ID: {visitDetails.related_data.client._id}
                                    </Typography>
                                </>
                            )}

                            {visitDetails.related_data?.clinician && (
                                <>
                                    <Divider sx={{ my: 2 }} />
                                    <Typography variant="h6" gutterBottom>
                                        Clinician Information
                                    </Typography>
                                    <Typography variant="body1">
                                        Clinician ID: {visitDetails.related_data.clinician._id}
                                    </Typography>
                                </>
                            )}
                        </Box>
                    )}
                </DialogContent>
                <DialogActions>
                    <Button onClick={() => setShowVisitDialog(false)}>
                        Close
                    </Button>
                </DialogActions>
            </Dialog>
        </Box>
    );
};

export default VisitReportPage;
