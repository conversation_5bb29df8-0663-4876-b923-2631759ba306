import { getAccessToken } from './getAccessToken';

// Helper to get base URL
function getBaseUrl() {
  const baseUrl = import.meta.env.VITE_BASE_URL;
  if (baseUrl) {
    return baseUrl.replace(/\/?$/, '/');
  } else {
    // Always use protocol://hostname:port/v1/
    const { protocol, hostname, port } = window.location;
    return `${protocol}//${hostname}${port ? `:${port}` : ''}/v1/`;
  }
}

// Simple global flag to prevent multiple redirects
let isRedirecting = false;

export async function apiClient<T = unknown>(
  endpoint: string,
  options: RequestInit = {},
  requireAuth: boolean = true
): Promise<T> {
  const url = getBaseUrl() + endpoint.replace(/^\//, '');
  let extraHeaders: Record<string, string> = {};
  if (options.headers && typeof options.headers === 'object' && !(options.headers instanceof Headers)) {
    extraHeaders = Object.fromEntries(Object.entries(options.headers).filter(([, v]) => typeof v === 'string'));
  }
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    ...extraHeaders,
  };
  if (requireAuth) {
    const token = getAccessToken();
    if (token) headers['Authorization'] = `Bearer ${token}`;
  }

  const response = await fetch(url, { ...options, headers });

  if (!response.ok) {
    // Handle 401 specifically - session expired
    if (response.status === 401 && !isRedirecting) {
      isRedirecting = true;

      // Clear all auth data
      localStorage.removeItem('adminUser');
      localStorage.removeItem('accessToken');

      // Redirect to signin
      window.location.href = '/signin';

      // Reset flag after redirect
      setTimeout(() => { isRedirecting = false; }, 1000);
    }

    const error = await response.text();
    throw new Error(error || 'API request failed');
  }

  return response.json();
}

// Tenant Reports API functions
export interface TenantInfo {
  tenantId: string;
  tenantName: string;
}

export interface VisitInfo {
  _id: string;
  episodeId: string;
  clinicianId: string;
  clientId: string;
  visitNo: string;
  visitDate: string;
  visitStartTime: string;
  visitType: string;
  status?: string;  // Optional since some records don't have status
  createdAt: string;
  updatedAt: string;
}

export interface TenantReportResponse {
  tenant: TenantInfo;
  visits: VisitInfo[];
  total_visits: number;
  visits_by_status: Record<string, number>;
  visits_by_type: Record<string, number>;
}



// Tenant Reports API functions
export const tenantReportsApi = {
  // Get all tenants
  getTenants: async (): Promise<TenantInfo[]> => {
    return apiClient<TenantInfo[]>('tenant-reports/tenants');
  },

  // Get tenant visits
  getTenantVisits: async (params: {
    tenant_id: string;
    status?: string;
    visit_type?: string;
    start_date?: string;
    end_date?: string;
    limit?: number;
    offset?: number;
  }): Promise<TenantReportResponse> => {
    const { tenant_id, ...queryParams } = params;
    const searchParams = new URLSearchParams();

    if (queryParams.status) searchParams.append('status', queryParams.status);
    if (queryParams.visit_type) searchParams.append('visit_type', queryParams.visit_type);
    if (queryParams.start_date) searchParams.append('start_date', queryParams.start_date);
    if (queryParams.end_date) searchParams.append('end_date', queryParams.end_date);
    if (queryParams.limit) searchParams.append('limit', queryParams.limit.toString());
    if (queryParams.offset) searchParams.append('offset', queryParams.offset.toString());

    const queryString = searchParams.toString();
    const endpoint = `tenant-reports/tenant/${tenant_id}/visits${queryString ? `?${queryString}` : ''}`;
    return apiClient<TenantReportResponse>(endpoint);
  },

  // Get visit details
  getVisitDetails: async (tenant_id: string, visit_id: string): Promise<any> => {
    return apiClient<any>(`tenant-reports/tenant/${tenant_id}/visit/${visit_id}`);
  },

  // Get tenant analytics
  getTenantAnalytics: async (params: {
    tenant_id: string;
    start_date?: string;
    end_date?: string;
  }): Promise<any> => {
    const { tenant_id, ...queryParams } = params;
    const searchParams = new URLSearchParams();

    if (queryParams.start_date) searchParams.append('start_date', queryParams.start_date);
    if (queryParams.end_date) searchParams.append('end_date', queryParams.end_date);

    const queryString = searchParams.toString();
    const endpoint = `tenant-reports/tenant/${tenant_id}/analytics${queryString ? `?${queryString}` : ''}`;
    return apiClient<any>(endpoint);
  },

  // Export tenant data
  exportTenantData: async (params: {
    tenant_id: string;
    data_type: string;
    format: string;
    start_date?: string;
    end_date?: string;
  }): Promise<any> => {
    const { tenant_id, ...queryParams } = params;
    const searchParams = new URLSearchParams();

    searchParams.append('data_type', queryParams.data_type);
    searchParams.append('format', queryParams.format);
    if (queryParams.start_date) searchParams.append('start_date', queryParams.start_date);
    if (queryParams.end_date) searchParams.append('end_date', queryParams.end_date);

    const queryString = searchParams.toString();
    const endpoint = `tenant-reports/tenant/${tenant_id}/export?${queryString}`;
    return apiClient<any>(endpoint);
  }
};
