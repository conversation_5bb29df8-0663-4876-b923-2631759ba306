version: '3.8'

services:
  ai-core:
    build:
      context: ../../ # Build from the parent directory to access packages
      dockerfile: apps/ai_core/Dockerfile
    ports:
      - "8001:8001"
    volumes:
      - ./ssl:/app/apps/ai_core/ssl # Mount SSL directory as read-only
      - ./data:/app/apps/ai_core/data # Mount data directory for persistence
    environment:
      - VERSION=${VERSION:-latest}
      # - PYTHONPATH=/app/packages:/app/apps
    env_file:
      - .env
    restart: unless-stopped
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:8001/v1/version" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - backend

  # TODO - add nginx-proxy-manager service
  nginx-proxy-manager:
    image: 'jc21/nginx-proxy-manager:latest'
    restart: unless-stopped
    ports:
      - '80:80' # Public HTTP Port
      - '81:81' # Admin Web UI
      - '443:443' # Public HTTPS Port
    environment:
      - TZ=America/Los_Angeles
    volumes:
      - /home/<USER>/ai_core_persistent/nginx-data:/data
      - /home/<USER>/ai_core_persistent/nginx-letsencrypt:/etc/letsencrypt

networks:
  backend:
    external: true
