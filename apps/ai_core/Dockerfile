FROM python:3.10
# FROM public.ecr.aws/lambda/python:3.10

WORKDIR /app

COPY apps/ai_core /app/apps/ai_core
COPY packages/auth /app/packages/auth
COPY packages/config /app/packages/config
COPY packages/database /app/packages/database
COPY packages/messages /app/packages/messages
COPY packages/models /app/packages/models
COPY packages/monitor /app/packages/monitor
COPY packages/queues /app/packages/queues

RUN pip install --upgrade pip

RUN pip install -e apps/ai_core \
    && pip install -e packages/auth \
    && pip install -e packages/config \
    && pip install -e packages/database \
    && pip install -e packages/messages \
    && pip install -e packages/models \
    && pip install -e packages/monitor \
    && pip install -e packages/queues


WORKDIR /app/apps/ai_core

EXPOSE 8001

CMD ["python", "-m", "ai_core.main"]
