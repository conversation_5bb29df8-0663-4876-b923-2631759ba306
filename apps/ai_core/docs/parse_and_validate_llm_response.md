# parse_and_validate_llm_response Function

## Overview

The `parse_and_validate_llm_response` function has been extracted from the main `answer_questions` function to enable independent testing of JSON parsing and validation logic. This separation allows for better unit testing, mock response testing, and exception scenario handling.

## Function Signature

```python
def parse_and_validate_llm_response(response: str, question_answer_list: list) -> bool:
    """
    Parse and validate LLM response JSON, adding valid items to question_answer_list.
    
    Args:
        response: Raw response string from LLM
        question_answer_list: List to append valid question-answer items to
        
    Returns:
        bool: True if parsing and validation succeeded, False otherwise
        
    Raises:
        Exception: Re-raises any validation or parsing exceptions for testing
    """
```

## What the Function Does

1. **Input Validation**: Checks if the response is empty or None
2. **String Cleaning**: Removes markdown code block markers (```) and "json" keywords
3. **JSON Parsing**: Converts the cleaned string to JSON data
4. **Schema Validation**: Validates against either `response_schema` (for lists) or `single_response_schema` (for single objects)
5. **Data Appending**: Adds valid items to the provided list
6. **Error Handling**: Logs errors and re-raises exceptions for testing purposes

## Usage Examples

### Basic Usage

```python
from ai_core.answer_questions_langchain_knowledgebase_subgrouping import parse_and_validate_llm_response

# Prepare a list to collect results
question_answer_list = []

# Mock LLM response
mock_response = '''```json
{
    "question_code": "M1021",
    "question_text": "Primary Diagnosis",
    "question_type": "text",
    "answer_context": "Patient has diabetes",
    "answer_reason": "Based on medical history",
    "answer_text": "Diabetes mellitus",
    "confidence_score": 0.95
}
```'''

# Parse and validate
try:
    success = parse_and_validate_llm_response(mock_response, question_answer_list)
    if success:
        print(f"Successfully parsed {len(question_answer_list)} items")
    else:
        print("Parsing failed")
except Exception as e:
    print(f"Exception occurred: {e}")
```

### Testing with Mock Responses

```python
import json
from unittest.mock import patch

# Test data
test_responses = [
    {
        "question_code": "M1021",
        "question_text": "Primary Diagnosis",
        "question_type": "text",
        "answer_context": "Patient has diabetes",
        "answer_reason": "Based on medical history",
        "answer_text": "Diabetes mellitus",
        "confidence_score": 0.95
    }
]

# Create mock response string
mock_response = f"```json\n{json.dumps(test_responses)}\n```"

# Test with mocked logger to avoid actual logging
question_answer_list = []
with patch('ai_core.answer_questions_langchain_knowledgebase_subgrouping.logger'):
    result = parse_and_validate_llm_response(mock_response, question_answer_list)
    
print(f"Result: {result}")
print(f"Items parsed: {len(question_answer_list)}")
```

## Exception Scenarios for Testing

### 1. Empty Response
```python
result = parse_and_validate_llm_response("", question_answer_list)
# Returns: False
```

### 2. Invalid JSON
```python
try:
    parse_and_validate_llm_response("```json\n{invalid json\n```", question_answer_list)
except json.JSONDecodeError as e:
    print(f"JSON parsing failed: {e}")
```

### 3. Schema Validation Failure
```python
# Missing required field 'question_code'
invalid_response = '''```json
{
    "question_text": "Primary Diagnosis",
    "question_type": "text",
    "answer_context": "Patient has diabetes",
    "answer_reason": "Based on medical history",
    "answer_text": "Diabetes mellitus",
    "confidence_score": 0.95
}
```'''

try:
    parse_and_validate_llm_response(invalid_response, question_answer_list)
except jsonschema.ValidationError as e:
    print(f"Schema validation failed: {e}")
```

## Testing Files

### Unit Tests
- **Location**: `apps/ai_core/tests/test_parse_and_validate_llm_response.py`
- **Purpose**: Comprehensive unit tests using pytest
- **Coverage**: All success and failure scenarios

### Example Script
- **Location**: `apps/ai_core/examples/test_parse_function_example.py`
- **Purpose**: Practical demonstration of function usage
- **Features**: Interactive testing with console output

## Running Tests

### Using pytest
```bash
cd apps/ai_core
python -m pytest tests/test_parse_and_validate_llm_response.py -v
```

### Using the example script
```bash
cd apps/ai_core
python examples/test_parse_function_example.py
```

## Benefits of Extraction

1. **Testability**: Function can be tested independently with mock data
2. **Maintainability**: Logic is centralized and easier to modify
3. **Reusability**: Can be used in other parts of the codebase
4. **Error Handling**: Better isolation of parsing errors
5. **Debugging**: Easier to debug JSON parsing issues

## Schema Requirements

The function validates against two schemas:

- **`response_schema`**: For array responses (list of question-answer objects)
- **`single_response_schema`**: For single object responses

Both schemas require these fields:
- `question_code`: String identifier
- `question_text`: The question text
- `question_type`: Type of question
- `answer_context`: Context for the answer
- `answer_reason`: Reasoning for the answer
- `answer_text`: The actual answer
- `confidence_score`: Confidence level (0.0 to 1.0)
