{"clinician_observation_config": {"Mobility": {"enabled": true, "search_queries": ["does the patient use wheelchair or walker or cane?"], "observation_prompt": "Based on the conversation context, identify any mobility aids (wheelchair, walker, cane) or assistive devices the patient uses and their mobility status."}, "ADLs Assessment": {"enabled": true, "search_queries": ["does the patient use wheelchair or walker or cane? or any other assistive device for mobility"], "observation_prompt": "Based on the conversation context, identify the patient's level of independence or assistance needed for activities of daily living including bathing, dressing, eating, toileting, transferring, walking, grooming, medication management, and instrumental activities like housekeeping and shopping."}}}