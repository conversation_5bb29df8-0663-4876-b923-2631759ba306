# managed
import logging
from fastapi import Request, APIRouter, HTTPException, Depends, Security
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import List, Union, Dict, Any
import uuid
from config import create_app_settings
from database.interface import DatabaseAdapter
from database.factory import get_database_client
from queues.factory import get_queue_client
from queues.interface import QueueClient
#from ai_core.models.registration import Registration
from ai_core.models.config import Config
from ai_core.models.registration import Registration
from pydantic import BaseModel
from typing import Dict
from auth.factory import get_auth_provider
from ai_core.auth_util import require_role, no_role_required
from ai_core.invoker import safe_invoke
from ai_core.service_response import ServiceResponseMessage

settings, config_provider = create_app_settings("ai_core", Config)


logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter()

auth = get_auth_provider(config_provider)
security = HTTPBearer()


# write - Create an item
@router.post("/registration", response_model=Union[Registration, ServiceResponseMessage])
def create_registration(item: Registration, 
                        request: Request,
                        user: dict = Depends(require_role([]) if settings.auth_enabled else no_role_required)):
    logger.debug(f"--> create_registration: {item}")
    try:
        ret = safe_invoke("ai_core.services.registration_service", "create_registration", [item, user, request])
    except Exception as e:
        logger.error(f"Error creating registration: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    if isinstance(ret, ServiceResponseMessage) and (ret.status_code is not None and ret.status_code >= 300):
        raise HTTPException(status_code=ret.status_code, detail=ret.dict())
    return ret


# read - Retrieve all items
@router.get("/registration-list", response_model=Union[List[Registration]])
def get_all_registrations(request:Request,
                        user: dict = Depends(require_role([]) if settings.auth_enabled else no_role_required)):
    logger.debug("--> get_all_registrations")
    try:
        ret = safe_invoke("ai_core.services.registration_service", "get_all_registration", [user, request])
    except Exception as e:
        logger.error(f"Error retrieving all registrations: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    if isinstance(ret, ServiceResponseMessage) and (ret.status_code is not None and ret.status_code >= 300):
        raise HTTPException(status_code=ret.status_code, detail=ret.dict())
    return ret


# read - Retrieve a single item
@router.get("/registration/{id}", response_model=Union[Registration, ServiceResponseMessage])
def get_registration(id: str, request:Request,
                     user: dict = Depends(require_role([]) if settings.auth_enabled else no_role_required)):
    logger.debug(f"Received request to retrieve registration with id: {id}")
    try:
        ret =safe_invoke("ai_core.services.registration_service", "get_registration", [id, user, request])
    except Exception as e:
        logger.error(f"Error retrieving registration with id: {id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    if not ret:
        raise HTTPException(status_code=404, detail="Item not found")
    if isinstance(ret, ServiceResponseMessage) and (ret.status_code is not None and ret.status_code >= 300):
        raise HTTPException(status_code=ret.status_code, detail=ret.dict())
    logger.debug(f"Retrieved registration: {ret}")
    return ret

# read - Retrieve a single item
@router.get("/registration-path/{full_path:path}", response_model=Union[Registration, ServiceResponseMessage])
def get_registration(full_path: str, request:Request,
                     user: dict = Depends(require_role([]) if settings.auth_enabled else no_role_required)):
    logger.debug(f"Received request to retrieve registration with full_path: {full_path}")
    try:
        ret =safe_invoke("ai_core.services.registration_service", "get_path_registration", [full_path, user, request])
    except Exception as e:
        logger.error(f"Error retrieving registration with full_path: {full_path}: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    if not ret:
        raise HTTPException(status_code=404, detail="Item not found")
    if isinstance(ret, ServiceResponseMessage) and (ret.status_code is not None and ret.status_code >= 300):
        raise HTTPException(status_code=ret.status_code, detail=ret.dict())
    logger.debug(f"Retrieved registration: {ret}")
    return ret

# write - Update an item (without modifying ID)
@router.put("/registration/{id}", response_model=Union[Registration, ServiceResponseMessage])
def update_registration(id: str, request:Request,
                        updated_item: Registration, 
                        user: dict = Depends(require_role([]) if settings.auth_enabled else no_role_required)):
    logger.debug(f"Received request to update registration with id {id}: {updated_item}")
    try:
        ret = safe_invoke("ai_core.services.registration_service", "update_registration", [id, updated_item, user, request])
    except Exception as e:
        logger.error(f"Error updating registration with id {id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    if isinstance(ret, ServiceResponseMessage) and (ret.status_code is not None and ret.status_code >= 300):
        raise HTTPException(status_code=ret.status_code, detail=ret.dict())
    return ret

# write - Delete an item
@router.delete("/registration/{id}", response_model=Union[Registration, ServiceResponseMessage])
def delete_registration(id: str, request:Request,
                        user: dict = Depends(require_role([]) if settings.auth_enabled else no_role_required)):
    logger.debug(f"Received request to delete registration with id {id}")
    try:
        ret = safe_invoke("ai_core.services.registration_service", "delete_registration", [id, user, request])
    except Exception as e:
        logger.error(f"Error deleting registration with id {id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    if not ret:
        logger.warning(f"Registration with id {id} not found")
        raise HTTPException(status_code=404, detail="Item not found")
    if isinstance(ret, ServiceResponseMessage) and (ret.status_code is not None and ret.status_code >= 300):
        raise HTTPException(status_code=ret.status_code, detail=ret.dict())
    return ret


class RequestResponse(BaseModel):
    metadata: Dict[str, Any]
    records: List[Registration]

@router.post("/registration-request", response_model=RequestResponse)
def registration_request_handler(
    payload: Dict[str, Any],
    request: Request,
    user: dict = Depends(require_role([]) if settings.auth_enabled else no_role_required)
):
    logger.debug(f"--> registration_request_handler: payload={payload}")
    try:
        result = safe_invoke("ai_core.services.registration_service", "process_registration_request", [payload, user, request])
    except Exception as e:
        logger.error(f"Error processing registration-request: {e}")
        raise HTTPException(status_code=500, detail=str(e))

    if not isinstance(result, dict) or "metadata" not in result or "records" not in result:
        raise HTTPException(status_code=500, detail="Invalid response from registration service")

    return result
