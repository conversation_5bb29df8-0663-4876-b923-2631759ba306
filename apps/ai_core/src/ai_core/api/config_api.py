# managed
import logging
from fastapi import Request, APIRouter, HTTPException, Depends, Security
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import List, Union, Dict, Any
import uuid
from config import create_app_settings
from database.interface import DatabaseAdapter
from database.factory import get_database_client
from queues.factory import get_queue_client
from queues.interface import QueueClient
#from ai_core.models.config import Config
from ai_core.models.config import Config
from ai_core.models.config import Config
from pydantic import BaseModel
from typing import Dict
from auth.factory import get_auth_provider
from ai_core.auth_util import require_role, no_role_required
from ai_core.invoker import safe_invoke
from ai_core.service_response import ServiceResponseMessage

settings, config_provider = create_app_settings("ai_core", Config)


logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter()

auth = get_auth_provider(config_provider)
security = HTTPBearer()


# write - Create an item
@router.post("/config", response_model=Union[Config, ServiceResponseMessage])
def create_config(item: Config, 
                        request: Request,
                        user: dict = Depends(require_role([]) if settings.auth_enabled else no_role_required)):
    logger.debug(f"--> create_config: {item}")
    try:
        ret = safe_invoke("ai_core.services.config_service", "create_config", [item, user, request])
    except Exception as e:
        logger.error(f"Error creating config: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    if isinstance(ret, ServiceResponseMessage) and (ret.status_code is not None and ret.status_code >= 300):
        raise HTTPException(status_code=ret.status_code, detail=ret.dict())
    return ret


# read - Retrieve all items
@router.get("/config-list", response_model=Union[List[Config]])
def get_all_configs(request:Request,
                        user: dict = Depends(require_role([]) if settings.auth_enabled else no_role_required)):
    logger.debug("--> get_all_configs")
    try:
        ret = safe_invoke("ai_core.services.config_service", "get_all_config", [user, request])
    except Exception as e:
        logger.error(f"Error retrieving all configs: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    if isinstance(ret, ServiceResponseMessage) and (ret.status_code is not None and ret.status_code >= 300):
        raise HTTPException(status_code=ret.status_code, detail=ret.dict())
    return ret


# read - Retrieve a single item
@router.get("/config/{id}", response_model=Union[Config, ServiceResponseMessage])
def get_config(id: str, request:Request,
                     user: dict = Depends(require_role([]) if settings.auth_enabled else no_role_required)):
    logger.debug(f"Received request to retrieve config with id: {id}")
    try:
        ret =safe_invoke("ai_core.services.config_service", "get_config", [id, user, request])
    except Exception as e:
        logger.error(f"Error retrieving config with id: {id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    if not ret:
        raise HTTPException(status_code=404, detail="Item not found")
    if isinstance(ret, ServiceResponseMessage) and (ret.status_code is not None and ret.status_code >= 300):
        raise HTTPException(status_code=ret.status_code, detail=ret.dict())
    logger.debug(f"Retrieved config: {ret}")
    return ret

# read - Retrieve a single item
@router.get("/config-path/{full_path:path}", response_model=Union[Config, ServiceResponseMessage])
def get_config(full_path: str, request:Request,
                     user: dict = Depends(require_role([]) if settings.auth_enabled else no_role_required)):
    logger.debug(f"Received request to retrieve config with full_path: {full_path}")
    try:
        ret =safe_invoke("ai_core.services.config_service", "get_path_config", [full_path, user, request])
    except Exception as e:
        logger.error(f"Error retrieving config with full_path: {full_path}: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    if not ret:
        raise HTTPException(status_code=404, detail="Item not found")
    if isinstance(ret, ServiceResponseMessage) and (ret.status_code is not None and ret.status_code >= 300):
        raise HTTPException(status_code=ret.status_code, detail=ret.dict())
    logger.debug(f"Retrieved config: {ret}")
    return ret

# write - Update an item (without modifying ID)
@router.put("/config/{id}", response_model=Union[Config, ServiceResponseMessage])
def update_config(id: str, request:Request,
                        updated_item: Config, 
                        user: dict = Depends(require_role([]) if settings.auth_enabled else no_role_required)):
    logger.debug(f"Received request to update config with id {id}: {updated_item}")
    try:
        ret = safe_invoke("ai_core.services.config_service", "update_config", [id, updated_item, user, request])
    except Exception as e:
        logger.error(f"Error updating config with id {id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    if isinstance(ret, ServiceResponseMessage) and (ret.status_code is not None and ret.status_code >= 300):
        raise HTTPException(status_code=ret.status_code, detail=ret.dict())
    return ret

# write - Delete an item
@router.delete("/config/{id}", response_model=Union[Config, ServiceResponseMessage])
def delete_config(id: str, request:Request,
                        user: dict = Depends(require_role([]) if settings.auth_enabled else no_role_required)):
    logger.debug(f"Received request to delete config with id {id}")
    try:
        ret = safe_invoke("ai_core.services.config_service", "delete_config", [id, user, request])
    except Exception as e:
        logger.error(f"Error deleting config with id {id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    if not ret:
        logger.warning(f"Config with id {id} not found")
        raise HTTPException(status_code=404, detail="Item not found")
    if isinstance(ret, ServiceResponseMessage) and (ret.status_code is not None and ret.status_code >= 300):
        raise HTTPException(status_code=ret.status_code, detail=ret.dict())
    return ret


class RequestResponse(BaseModel):
    metadata: Dict[str, Any]
    records: List[Config]

@router.post("/config-request", response_model=RequestResponse)
def config_request_handler(
    payload: Dict[str, Any],
    request: Request,
    user: dict = Depends(require_role([]) if settings.auth_enabled else no_role_required)
):
    logger.debug(f"--> config_request_handler: payload={payload}")
    try:
        result = safe_invoke("ai_core.services.config_service", "process_config_request", [payload, user, request])
    except Exception as e:
        logger.error(f"Error processing config-request: {e}")
        raise HTTPException(status_code=500, detail=str(e))

    if not isinstance(result, dict) or "metadata" not in result or "records" not in result:
        raise HTTPException(status_code=500, detail="Invalid response from config service")

    return result
