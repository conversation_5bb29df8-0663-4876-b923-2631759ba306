# managed
import logging
from fastapi import Request, APIRouter, HTTPException, Depends, Security
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import List, Union, Dict, Any
import uuid
from config import create_app_settings
from database.interface import DatabaseAdapter
from database.factory import get_database_client
from queues.factory import get_queue_client
from queues.interface import QueueClient
#from ai_core.models.transcription_request import TranscriptionRequest
from ai_core.models.config import Config
from ai_core.models.transcription_request import TranscriptionRequest
from pydantic import BaseModel
from typing import Dict
from auth.factory import get_auth_provider
from ai_core.auth_util import require_role, no_role_required
from ai_core.invoker import safe_invoke
from ai_core.service_response import ServiceResponseMessage

settings, config_provider = create_app_settings("ai_core", Config)


logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter()

auth = get_auth_provider(config_provider)
security = HTTPBearer()


# write - Create an item
@router.post("/transcription-request", response_model=Union[TranscriptionRequest, ServiceResponseMessage])
def create_transcription_request(item: TranscriptionRequest, 
                        request: Request,
                        user: dict = Depends(require_role([]) if settings.auth_enabled else no_role_required)):
    logger.debug(f"--> create_transcription_request: {item}")
    try:
        ret = safe_invoke("ai_core.services.transcription_request_service", "create_transcription_request", [item, user, request])
    except Exception as e:
        logger.error(f"Error creating transcription_request: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    if isinstance(ret, ServiceResponseMessage) and (ret.status_code is not None and ret.status_code >= 300):
        raise HTTPException(status_code=ret.status_code, detail=ret.dict())
    return ret


# read - Retrieve all items
@router.get("/transcription-request-list", response_model=Union[List[TranscriptionRequest]])
def get_all_transcription_requests(request:Request,
                        user: dict = Depends(require_role([]) if settings.auth_enabled else no_role_required)):
    logger.debug("--> get_all_transcription_requests")
    try:
        ret = safe_invoke("ai_core.services.transcription_request_service", "get_all_transcription_request", [user, request])
    except Exception as e:
        logger.error(f"Error retrieving all transcription_requests: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    if isinstance(ret, ServiceResponseMessage) and (ret.status_code is not None and ret.status_code >= 300):
        raise HTTPException(status_code=ret.status_code, detail=ret.dict())
    return ret


# read - Retrieve a single item
@router.get("/transcription-request/{id}", response_model=Union[TranscriptionRequest, ServiceResponseMessage])
def get_transcription_request(id: str, request:Request,
                     user: dict = Depends(require_role([]) if settings.auth_enabled else no_role_required)):
    logger.debug(f"Received request to retrieve transcription_request with id: {id}")
    try:
        ret =safe_invoke("ai_core.services.transcription_request_service", "get_transcription_request", [id, user, request])
    except Exception as e:
        logger.error(f"Error retrieving transcription_request with id: {id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    if not ret:
        raise HTTPException(status_code=404, detail="Item not found")
    if isinstance(ret, ServiceResponseMessage) and (ret.status_code is not None and ret.status_code >= 300):
        raise HTTPException(status_code=ret.status_code, detail=ret.dict())
    logger.debug(f"Retrieved transcription_request: {ret}")
    return ret

# read - Retrieve a single item
@router.get("/transcription-request-path/{full_path:path}", response_model=Union[TranscriptionRequest, ServiceResponseMessage])
def get_transcription_request(full_path: str, request:Request,
                     user: dict = Depends(require_role([]) if settings.auth_enabled else no_role_required)):
    logger.debug(f"Received request to retrieve transcription_request with full_path: {full_path}")
    try:
        ret =safe_invoke("ai_core.services.transcription_request_service", "get_path_transcription_request", [full_path, user, request])
    except Exception as e:
        logger.error(f"Error retrieving transcription_request with full_path: {full_path}: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    if not ret:
        raise HTTPException(status_code=404, detail="Item not found")
    if isinstance(ret, ServiceResponseMessage) and (ret.status_code is not None and ret.status_code >= 300):
        raise HTTPException(status_code=ret.status_code, detail=ret.dict())
    logger.debug(f"Retrieved transcription_request: {ret}")
    return ret

# write - Update an item (without modifying ID)
@router.put("/transcription-request/{id}", response_model=Union[TranscriptionRequest, ServiceResponseMessage])
def update_transcription_request(id: str, request:Request,
                        updated_item: TranscriptionRequest, 
                        user: dict = Depends(require_role([]) if settings.auth_enabled else no_role_required)):
    logger.debug(f"Received request to update transcription_request with id {id}: {updated_item}")
    try:
        ret = safe_invoke("ai_core.services.transcription_request_service", "update_transcription_request", [id, updated_item, user, request])
    except Exception as e:
        logger.error(f"Error updating transcription_request with id {id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    if isinstance(ret, ServiceResponseMessage) and (ret.status_code is not None and ret.status_code >= 300):
        raise HTTPException(status_code=ret.status_code, detail=ret.dict())
    return ret

# write - Delete an item
@router.delete("/transcription-request/{id}", response_model=Union[TranscriptionRequest, ServiceResponseMessage])
def delete_transcription_request(id: str, request:Request,
                        user: dict = Depends(require_role([]) if settings.auth_enabled else no_role_required)):
    logger.debug(f"Received request to delete transcription_request with id {id}")
    try:
        ret = safe_invoke("ai_core.services.transcription_request_service", "delete_transcription_request", [id, user, request])
    except Exception as e:
        logger.error(f"Error deleting transcription_request with id {id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    if not ret:
        logger.warning(f"TranscriptionRequest with id {id} not found")
        raise HTTPException(status_code=404, detail="Item not found")
    if isinstance(ret, ServiceResponseMessage) and (ret.status_code is not None and ret.status_code >= 300):
        raise HTTPException(status_code=ret.status_code, detail=ret.dict())
    return ret


class RequestResponse(BaseModel):
    metadata: Dict[str, Any]
    records: List[TranscriptionRequest]

@router.post("/transcription-request-request", response_model=RequestResponse)
def transcription_request_request_handler(
    payload: Dict[str, Any],
    request: Request,
    user: dict = Depends(require_role([]) if settings.auth_enabled else no_role_required)
):
    logger.debug(f"--> transcription_request_request_handler: payload={payload}")
    try:
        result = safe_invoke("ai_core.services.transcription_request_service", "process_transcription_request_request", [payload, user, request])
    except Exception as e:
        logger.error(f"Error processing transcription-request-request: {e}")
        raise HTTPException(status_code=500, detail=str(e))

    if not isinstance(result, dict) or "metadata" not in result or "records" not in result:
        raise HTTPException(status_code=500, detail="Invalid response from transcription_request service")

    return result


# Test endpoint for answer_questions functionality with M-GG flagging
@router.post("/answer-questions-test", response_model=Dict[str, Any])
def test_answer_questions(
    payload: Dict[str, Any],
    request: Request,
    user: dict = Depends(require_role([]) if settings.auth_enabled else no_role_required)
):
    """
    Test endpoint for answer_questions functionality with M-GG flagging

    Expected payload:
    {
        "conversation_data": {
            "conversation": [
                {"speaker": "clinician", "text": "..."},
                {"speaker": "patient", "text": "..."}
            ]
        },
        "questions": [
            {
                "question_code": "M1810",
                "question": "...",
                "question_type": "radio-group",
                "options": [...]
            }
        ],
        "client_id": "test-client",
        "assessment_id": "test-assessment"
    }
    """
    logger.debug(f"--> test_answer_questions: payload keys={list(payload.keys())}")

    try:
        from ai_core.answer_questions import answer_questions

        conversation_data = payload.get("conversation_data")
        questions = payload.get("questions")
        client_id = payload.get("client_id", "test-client")
        assessment_id = payload.get("assessment_id", "test-assessment")

        if not conversation_data:
            raise HTTPException(status_code=400, detail="Missing conversation_data")
        if not questions:
            raise HTTPException(status_code=400, detail="Missing questions")

        logger.info(f"Processing {len(questions)} questions for client {client_id}")

        # Call the answer_questions function
        result = answer_questions(conversation_data, questions, client_id, assessment_id)

        if not result:
            raise HTTPException(status_code=500, detail="Answer generation failed")

        # The result should include flagging_results from your implementation
        return {
            "success": True,
            "client_id": client_id,
            "assessment_id": assessment_id,
            "questions_processed": len(questions),
            "result": result
        }

    except Exception as e:
        logger.error(f"Error in answer_questions test: {e}")
        raise HTTPException(status_code=500, detail=str(e))
