# managed
import logging
from fastapi import Request, APIRouter, HTTPException, Depends, Security
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import List, Union, Dict, Any
import uuid
from config import create_app_settings
from database.interface import DatabaseAdapter
from database.factory import get_database_client
from queues.factory import get_queue_client
from queues.interface import QueueClient
#from ai_core.models.file import File
from ai_core.models.config import Config
from ai_core.models.file import File
from pydantic import BaseModel
from typing import Dict
from auth.factory import get_auth_provider
from ai_core.auth_util import require_role, no_role_required
from ai_core.invoker import safe_invoke
from ai_core.service_response import ServiceResponseMessage

settings, config_provider = create_app_settings("ai_core", Config)


logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter()

auth = get_auth_provider(config_provider)
security = HTTPBearer()


# write - Create an item
@router.post("/file", response_model=Union[File, ServiceResponseMessage])
def create_file(item: File, 
                        request: Request,
                        user: dict = Depends(require_role([]) if settings.auth_enabled else no_role_required)):
    logger.debug(f"--> create_file: {item}")
    try:
        ret = safe_invoke("ai_core.services.file_service", "create_file", [item, user, request])
    except Exception as e:
        logger.error(f"Error creating file: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    if isinstance(ret, ServiceResponseMessage) and (ret.status_code is not None and ret.status_code >= 300):
        raise HTTPException(status_code=ret.status_code, detail=ret.dict())
    return ret


# read - Retrieve all items
@router.get("/file-list", response_model=Union[List[File]])
def get_all_files(request:Request,
                        user: dict = Depends(require_role([]) if settings.auth_enabled else no_role_required)):
    logger.debug("--> get_all_files")
    try:
        ret = safe_invoke("ai_core.services.file_service", "get_all_file", [user, request])
    except Exception as e:
        logger.error(f"Error retrieving all files: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    if isinstance(ret, ServiceResponseMessage) and (ret.status_code is not None and ret.status_code >= 300):
        raise HTTPException(status_code=ret.status_code, detail=ret.dict())
    return ret


# read - Retrieve a single item
@router.get("/file/{id}", response_model=Union[File, ServiceResponseMessage])
def get_file(id: str, request:Request,
                     user: dict = Depends(require_role([]) if settings.auth_enabled else no_role_required)):
    logger.debug(f"Received request to retrieve file with id: {id}")
    try:
        ret =safe_invoke("ai_core.services.file_service", "get_file", [id, user, request])
    except Exception as e:
        logger.error(f"Error retrieving file with id: {id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    if not ret:
        raise HTTPException(status_code=404, detail="Item not found")
    if isinstance(ret, ServiceResponseMessage) and (ret.status_code is not None and ret.status_code >= 300):
        raise HTTPException(status_code=ret.status_code, detail=ret.dict())
    logger.debug(f"Retrieved file: {ret}")
    return ret

# read - Retrieve a single item
@router.get("/file-path/{full_path:path}", response_model=Union[File, ServiceResponseMessage])
def get_file(full_path: str, request:Request,
                     user: dict = Depends(require_role([]) if settings.auth_enabled else no_role_required)):
    logger.debug(f"Received request to retrieve file with full_path: {full_path}")
    try:
        ret =safe_invoke("ai_core.services.file_service", "get_path_file", [full_path, user, request])
    except Exception as e:
        logger.error(f"Error retrieving file with full_path: {full_path}: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    if not ret:
        raise HTTPException(status_code=404, detail="Item not found")
    if isinstance(ret, ServiceResponseMessage) and (ret.status_code is not None and ret.status_code >= 300):
        raise HTTPException(status_code=ret.status_code, detail=ret.dict())
    logger.debug(f"Retrieved file: {ret}")
    return ret

# write - Update an item (without modifying ID)
@router.put("/file/{id}", response_model=Union[File, ServiceResponseMessage])
def update_file(id: str, request:Request,
                        updated_item: File, 
                        user: dict = Depends(require_role([]) if settings.auth_enabled else no_role_required)):
    logger.debug(f"Received request to update file with id {id}: {updated_item}")
    try:
        ret = safe_invoke("ai_core.services.file_service", "update_file", [id, updated_item, user, request])
    except Exception as e:
        logger.error(f"Error updating file with id {id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    if isinstance(ret, ServiceResponseMessage) and (ret.status_code is not None and ret.status_code >= 300):
        raise HTTPException(status_code=ret.status_code, detail=ret.dict())
    return ret

# write - Delete an item
@router.delete("/file/{id}", response_model=Union[File, ServiceResponseMessage])
def delete_file(id: str, request:Request,
                        user: dict = Depends(require_role([]) if settings.auth_enabled else no_role_required)):
    logger.debug(f"Received request to delete file with id {id}")
    try:
        ret = safe_invoke("ai_core.services.file_service", "delete_file", [id, user, request])
    except Exception as e:
        logger.error(f"Error deleting file with id {id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    if not ret:
        logger.warning(f"File with id {id} not found")
        raise HTTPException(status_code=404, detail="Item not found")
    if isinstance(ret, ServiceResponseMessage) and (ret.status_code is not None and ret.status_code >= 300):
        raise HTTPException(status_code=ret.status_code, detail=ret.dict())
    return ret


class RequestResponse(BaseModel):
    metadata: Dict[str, Any]
    records: List[File]

@router.post("/file-request", response_model=RequestResponse)
def file_request_handler(
    payload: Dict[str, Any],
    request: Request,
    user: dict = Depends(require_role([]) if settings.auth_enabled else no_role_required)
):
    logger.debug(f"--> file_request_handler: payload={payload}")
    try:
        result = safe_invoke("ai_core.services.file_service", "process_file_request", [payload, user, request])
    except Exception as e:
        logger.error(f"Error processing file-request: {e}")
        raise HTTPException(status_code=500, detail=str(e))

    if not isinstance(result, dict) or "metadata" not in result or "records" not in result:
        raise HTTPException(status_code=500, detail="Invalid response from file service")

    return result
