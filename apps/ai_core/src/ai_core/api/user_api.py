# managed
import logging
from fastapi import Request, APIRouter, HTTPException, Depends, Security
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import List, Union, Dict, Any
import uuid
from config import create_app_settings
from database.interface import DatabaseAdapter
from database.factory import get_database_client
from queues.factory import get_queue_client
from queues.interface import QueueClient
#from ai_core.models.user import User
from ai_core.models.config import Config
from ai_core.models.user import User
from pydantic import BaseModel
from typing import Dict
from auth.factory import get_auth_provider
from ai_core.auth_util import require_role, no_role_required
from ai_core.invoker import safe_invoke
from ai_core.service_response import ServiceResponseMessage

settings, config_provider = create_app_settings("ai_core", Config)


logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter()

auth = get_auth_provider(config_provider)
security = HTTPBearer()


# write - Create an item
@router.post("/user", response_model=Union[User, ServiceResponseMessage])
def create_user(item: User, 
                        request: Request,
                        user: dict = Depends(require_role([]) if settings.auth_enabled else no_role_required)):
    logger.debug(f"--> create_user: {item}")
    try:
        ret = safe_invoke("ai_core.services.user_service", "create_user", [item, user, request])
    except Exception as e:
        logger.error(f"Error creating user: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    if isinstance(ret, ServiceResponseMessage) and (ret.status_code is not None and ret.status_code >= 300):
        raise HTTPException(status_code=ret.status_code, detail=ret.dict())
    return ret


# read - Retrieve all items
@router.get("/user-list", response_model=Union[List[User]])
def get_all_users(request:Request,
                        user: dict = Depends(require_role([]) if settings.auth_enabled else no_role_required)):
    logger.debug("--> get_all_users")
    try:
        ret = safe_invoke("ai_core.services.user_service", "get_all_user", [user, request])
    except Exception as e:
        logger.error(f"Error retrieving all users: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    if isinstance(ret, ServiceResponseMessage) and (ret.status_code is not None and ret.status_code >= 300):
        raise HTTPException(status_code=ret.status_code, detail=ret.dict())
    return ret


# read - Retrieve a single item
@router.get("/user/{id}", response_model=Union[User, ServiceResponseMessage])
def get_user(id: str, request:Request,
                     user: dict = Depends(require_role([]) if settings.auth_enabled else no_role_required)):
    logger.debug(f"Received request to retrieve user with id: {id}")
    try:
        ret =safe_invoke("ai_core.services.user_service", "get_user", [id, user, request])
    except Exception as e:
        logger.error(f"Error retrieving user with id: {id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    if not ret:
        raise HTTPException(status_code=404, detail="Item not found")
    if isinstance(ret, ServiceResponseMessage) and (ret.status_code is not None and ret.status_code >= 300):
        raise HTTPException(status_code=ret.status_code, detail=ret.dict())
    logger.debug(f"Retrieved user: {ret}")
    return ret

# read - Retrieve a single item
@router.get("/user-path/{full_path:path}", response_model=Union[User, ServiceResponseMessage])
def get_user(full_path: str, request:Request,
                     user: dict = Depends(require_role([]) if settings.auth_enabled else no_role_required)):
    logger.debug(f"Received request to retrieve user with full_path: {full_path}")
    try:
        ret =safe_invoke("ai_core.services.user_service", "get_path_user", [full_path, user, request])
    except Exception as e:
        logger.error(f"Error retrieving user with full_path: {full_path}: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    if not ret:
        raise HTTPException(status_code=404, detail="Item not found")
    if isinstance(ret, ServiceResponseMessage) and (ret.status_code is not None and ret.status_code >= 300):
        raise HTTPException(status_code=ret.status_code, detail=ret.dict())
    logger.debug(f"Retrieved user: {ret}")
    return ret

# write - Update an item (without modifying ID)
@router.put("/user/{id}", response_model=Union[User, ServiceResponseMessage])
def update_user(id: str, request:Request,
                        updated_item: User, 
                        user: dict = Depends(require_role([]) if settings.auth_enabled else no_role_required)):
    logger.debug(f"Received request to update user with id {id}: {updated_item}")
    try:
        ret = safe_invoke("ai_core.services.user_service", "update_user", [id, updated_item, user, request])
    except Exception as e:
        logger.error(f"Error updating user with id {id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    if isinstance(ret, ServiceResponseMessage) and (ret.status_code is not None and ret.status_code >= 300):
        raise HTTPException(status_code=ret.status_code, detail=ret.dict())
    return ret

# write - Delete an item
@router.delete("/user/{id}", response_model=Union[User, ServiceResponseMessage])
def delete_user(id: str, request:Request,
                        user: dict = Depends(require_role([]) if settings.auth_enabled else no_role_required)):
    logger.debug(f"Received request to delete user with id {id}")
    try:
        ret = safe_invoke("ai_core.services.user_service", "delete_user", [id, user, request])
    except Exception as e:
        logger.error(f"Error deleting user with id {id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    if not ret:
        logger.warning(f"User with id {id} not found")
        raise HTTPException(status_code=404, detail="Item not found")
    if isinstance(ret, ServiceResponseMessage) and (ret.status_code is not None and ret.status_code >= 300):
        raise HTTPException(status_code=ret.status_code, detail=ret.dict())
    return ret


class RequestResponse(BaseModel):
    metadata: Dict[str, Any]
    records: List[User]

@router.post("/user-request", response_model=RequestResponse)
def user_request_handler(
    payload: Dict[str, Any],
    request: Request,
    user: dict = Depends(require_role([]) if settings.auth_enabled else no_role_required)
):
    logger.debug(f"--> user_request_handler: payload={payload}")
    try:
        result = safe_invoke("ai_core.services.user_service", "process_user_request", [payload, user, request])
    except Exception as e:
        logger.error(f"Error processing user-request: {e}")
        raise HTTPException(status_code=500, detail=str(e))

    if not isinstance(result, dict) or "metadata" not in result or "records" not in result:
        raise HTTPException(status_code=500, detail="Invalid response from user service")

    return result
