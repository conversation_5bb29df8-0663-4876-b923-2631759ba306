# managed
import logging
from fastapi import Request, APIRouter, HTTPException, Depends, Security
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import List, Union, Dict, Any
import uuid
from config import create_app_settings
from database.interface import DatabaseAdapter
from database.factory import get_database_client
from queues.factory import get_queue_client
from queues.interface import QueueClient
#from ai_core.models.comment_annotations import CommentAnnotations
from ai_core.models.config import Config
from ai_core.models.comment_annotations import CommentAnnotations
from pydantic import BaseModel
from typing import Dict
from auth.factory import get_auth_provider
from ai_core.auth_util import require_role, no_role_required
from ai_core.invoker import safe_invoke
from ai_core.service_response import ServiceResponseMessage

settings, config_provider = create_app_settings("ai_core", Config)


logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter()

auth = get_auth_provider(config_provider)
security = HTTPBearer()


# write - Create an item
@router.post("/comment-annotations", response_model=Union[CommentAnnotations, ServiceResponseMessage])
def create_comment_annotations(item: CommentAnnotations, 
                        request: Request,
                        user: dict = Depends(require_role([]) if settings.auth_enabled else no_role_required)):
    logger.debug(f"--> create_comment_annotations: {item}")
    try:
        ret = safe_invoke("ai_core.services.comment_annotations_service", "create_comment_annotations", [item, user, request])
    except Exception as e:
        logger.error(f"Error creating comment_annotations: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    if isinstance(ret, ServiceResponseMessage) and (ret.status_code is not None and ret.status_code >= 300):
        raise HTTPException(status_code=ret.status_code, detail=ret.dict())
    return ret


# read - Retrieve all items
@router.get("/comment-annotations-list", response_model=Union[List[CommentAnnotations]])
def get_all_comment_annotationss(request:Request,
                        user: dict = Depends(require_role([]) if settings.auth_enabled else no_role_required)):
    logger.debug("--> get_all_comment_annotationss")
    try:
        ret = safe_invoke("ai_core.services.comment_annotations_service", "get_all_comment_annotations", [user, request])
    except Exception as e:
        logger.error(f"Error retrieving all comment_annotationss: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    if isinstance(ret, ServiceResponseMessage) and (ret.status_code is not None and ret.status_code >= 300):
        raise HTTPException(status_code=ret.status_code, detail=ret.dict())
    return ret


# read - Retrieve a single item
@router.get("/comment-annotations/{id}", response_model=Union[CommentAnnotations, ServiceResponseMessage])
def get_comment_annotations(id: str, request:Request,
                     user: dict = Depends(require_role([]) if settings.auth_enabled else no_role_required)):
    logger.debug(f"Received request to retrieve comment_annotations with id: {id}")
    try:
        ret =safe_invoke("ai_core.services.comment_annotations_service", "get_comment_annotations", [id, user, request])
    except Exception as e:
        logger.error(f"Error retrieving comment_annotations with id: {id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    if not ret:
        raise HTTPException(status_code=404, detail="Item not found")
    if isinstance(ret, ServiceResponseMessage) and (ret.status_code is not None and ret.status_code >= 300):
        raise HTTPException(status_code=ret.status_code, detail=ret.dict())
    logger.debug(f"Retrieved comment_annotations: {ret}")
    return ret

# read - Retrieve a single item
@router.get("/comment-annotations-path/{full_path:path}", response_model=Union[CommentAnnotations, ServiceResponseMessage])
def get_comment_annotations(full_path: str, request:Request,
                     user: dict = Depends(require_role([]) if settings.auth_enabled else no_role_required)):
    logger.debug(f"Received request to retrieve comment_annotations with full_path: {full_path}")
    try:
        ret =safe_invoke("ai_core.services.comment_annotations_service", "get_path_comment_annotations", [full_path, user, request])
    except Exception as e:
        logger.error(f"Error retrieving comment_annotations with full_path: {full_path}: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    if not ret:
        raise HTTPException(status_code=404, detail="Item not found")
    if isinstance(ret, ServiceResponseMessage) and (ret.status_code is not None and ret.status_code >= 300):
        raise HTTPException(status_code=ret.status_code, detail=ret.dict())
    logger.debug(f"Retrieved comment_annotations: {ret}")
    return ret

# write - Update an item (without modifying ID)
@router.put("/comment-annotations/{id}", response_model=Union[CommentAnnotations, ServiceResponseMessage])
def update_comment_annotations(id: str, request:Request,
                        updated_item: CommentAnnotations, 
                        user: dict = Depends(require_role([]) if settings.auth_enabled else no_role_required)):
    logger.debug(f"Received request to update comment_annotations with id {id}: {updated_item}")
    try:
        ret = safe_invoke("ai_core.services.comment_annotations_service", "update_comment_annotations", [id, updated_item, user, request])
    except Exception as e:
        logger.error(f"Error updating comment_annotations with id {id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    if isinstance(ret, ServiceResponseMessage) and (ret.status_code is not None and ret.status_code >= 300):
        raise HTTPException(status_code=ret.status_code, detail=ret.dict())
    return ret

# write - Delete an item
@router.delete("/comment-annotations/{id}", response_model=Union[CommentAnnotations, ServiceResponseMessage])
def delete_comment_annotations(id: str, request:Request,
                        user: dict = Depends(require_role([]) if settings.auth_enabled else no_role_required)):
    logger.debug(f"Received request to delete comment_annotations with id {id}")
    try:
        ret = safe_invoke("ai_core.services.comment_annotations_service", "delete_comment_annotations", [id, user, request])
    except Exception as e:
        logger.error(f"Error deleting comment_annotations with id {id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    if not ret:
        logger.warning(f"CommentAnnotations with id {id} not found")
        raise HTTPException(status_code=404, detail="Item not found")
    if isinstance(ret, ServiceResponseMessage) and (ret.status_code is not None and ret.status_code >= 300):
        raise HTTPException(status_code=ret.status_code, detail=ret.dict())
    return ret


class RequestResponse(BaseModel):
    metadata: Dict[str, Any]
    records: List[CommentAnnotations]

@router.post("/comment-annotations-request", response_model=RequestResponse)
def comment_annotations_request_handler(
    payload: Dict[str, Any],
    request: Request,
    user: dict = Depends(require_role([]) if settings.auth_enabled else no_role_required)
):
    logger.debug(f"--> comment_annotations_request_handler: payload={payload}")
    try:
        result = safe_invoke("ai_core.services.comment_annotations_service", "process_comment_annotations_request", [payload, user, request])
    except Exception as e:
        logger.error(f"Error processing comment-annotations-request: {e}")
        raise HTTPException(status_code=500, detail=str(e))

    if not isinstance(result, dict) or "metadata" not in result or "records" not in result:
        raise HTTPException(status_code=500, detail="Invalid response from comment_annotations service")

    return result
