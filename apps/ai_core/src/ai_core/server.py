import logging
import json
import os
import uuid
from fastapi import FastAP<PERSON>, Request, Depends, HTTPException, Security, WebSocket
from fastapi.middleware.cors import CORSMiddleware
from fastapi.exceptions import RequestValidationError
from fastapi.staticfiles import StaticFiles
from ai_core.service_response import info_message
from ai_core.invoker import safe_invoke
from fastapi.responses import FileResponse, Response
from ai_core.error_util import log_exception, create_error_response
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials, OAuth2PasswordBearer, OAuth2PasswordRequestForm
from ai_core.models.registration import Registration
from ai_core.models.user import User
from database.interface import DatabaseAdapter
from database.factory import get_database_client
from queues.factory import get_queue_client
from queues.interface import QueueClient
from auth.verify_email import send_verification_email, send_reset_password_email
from auth.mfa import generate_totp_secret, get_totp_uri, generate_qr_code
from config import create_app_settings
from ai_core.models.config import Config as AiCoreConfig
from ai_core.registration_service import verify_and_register

# FIXME - probably should be a static package definition vs in each app
from ai_core.service_response import ServiceResponseMessage
from auth.factory import get_auth_provider
from pydantic import BaseModel, Field
from typing import List, Optional, Literal
from auth.generate_ssl import generate_self_signed_cert
from contextlib import asynccontextmanager

import time

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

VERSION_FILE = "version.json"

def load_version():
    """Load the version from version.json safely."""
    try:
        if os.path.exists(VERSION_FILE):
            with open(VERSION_FILE, "r") as f:
                data = json.load(f)
                return data
    except (json.JSONDecodeError, IOError) as e:
        logger.warning(f"Error loading version.json: {e}")
    return {"version": "0.0.0"}

version = load_version()
logger.info(f"Loaded version: {version}")

# Define request models for proper API documentation
class LoginRequest(BaseModel):
    username: str = Field(..., description="Email or username for authentication")
    password: str = Field(..., description="User password")

class LoginResponse(BaseModel):
    access_token: Optional[str] = Field(None, description="JWT access token (provided when login is successful)")
    token_type: Optional[str] = Field(None, description="Token type, usually 'bearer'")
    mfa_required: Optional[bool] = Field(None, description="Indicates if MFA verification is required")
    message: Optional[str] = Field(None, description="Additional information message")
    refresh_token: Optional[str] = Field(None, description="Refresh token for obtaining new access tokens")

class MFAVerifyRequest(BaseModel):
    username: str
    code: str

class ForgotPasswordRequest(BaseModel):
    email: str

class ResetPasswordRequest(BaseModel):
    resetToken: str
    newPassword: str

class RefreshTokenRequest(BaseModel):
    refresh_token: str

class RefreshTokenResponse(BaseModel):
    access_token: str
    token_type: str = "bearer"
    refresh_token: Optional[str] = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    # TODO - "invoke" service methods to hook implementation
    print("Starting up...")  # Setup: DB connection, worker init, etc.
    yield
    print("Shutting down...")  # Teardown: close DB, cleanup tasks, etc.
    
class AppServer:
    def __init__(self, settings: AiCoreConfig):
        self.settings : AiCoreConfig = settings

        self.app = FastAPI(title="Ai Core", lifespan=lifespan, version=version.get("version", "0.0.0"))
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        self.auth = get_auth_provider(lambda: self.settings.model_dump())
        self.security = HTTPBearer()
        self._register_routes()
        if os.path.exists("build"):
            # FIXME - needs to work for vite too
            if os.path.exists(os.path.join("build", "static")):
            #self.app.mount("/", StaticFiles(directory="build", html=True), name="static")
                self.app.mount("/static", StaticFiles(directory="build/static"), name="static")
            elif os.path.exists(os.path.join("build", "assets")):
                self.app.mount("/assets", StaticFiles(directory="build/assets"), name="static")
            else:
                logger.warning("'build' directory not found. But no 'static' or 'assets' directory found. Static files will not be served.")
        else:
            logger.warning("'build' directory not found. Static files will not be served.")

    def _register_routes(self):
        @self.app.get("/v1/version")
        def get_version():
            return version

        @self.app.websocket("/ws")
        async def websocket_endpoint(websocket: WebSocket):
            await websocket.accept()
            while True:
                data = await websocket.receive_text()
                await websocket.send_text(f"Message received: {data}")

        @self.app.post("/v1/mfa/setup")
        def setup_mfa(item: User, db: DatabaseAdapter = Depends(self.get_db_provider)):
            item.id = item.id.lower()
            user = db.get_item("user", item.id)
            if not user:
                raise HTTPException(status_code=404, detail="User not found")
            secret = generate_totp_secret(item.id)
            user["totp_secret"] = secret
            user["is_mfa_enabled"] = True
            db.update_item("user", item.id, user)
            uri = get_totp_uri(item.id, secret, self.settings.base_url)
            return {"secret": secret, "otp_auth_url": uri}

        @self.app.get("/v1/mfa/qrcode")
        def get_qrcode(username: str, db: DatabaseAdapter = Depends(self.get_db_provider)):
            user = db.get_item("user", username)
            if not user or not user.totp_secret:
                raise HTTPException(status_code=404, detail="MFA not set up for this user")
            uri = get_totp_uri(username, user.totp_secret, self.settings.base_url)
            return generate_qr_code(uri)
        
        # Include API routes
        from ai_core.api.config_api import router as config_router
        self.app.include_router(config_router, prefix='/v1', tags=["Config"])
        from ai_core.api.file_api import router as file_router
        self.app.include_router(file_router, prefix='/v1', tags=["File"])
        from ai_core.api.registration_api import router as registration_router
        self.app.include_router(registration_router, prefix='/v1', tags=["Registration"])
        from ai_core.api.user_api import router as user_router
        self.app.include_router(user_router, prefix='/v1', tags=["User"])
        from ai_core.api.transcription_rating_api import router as transcription_rating_router
        self.app.include_router(transcription_rating_router, prefix='/v1', tags=["Transcription Rating"])
        from ai_core.api.comment_annotations_api import router as comment_annotations_router
        self.app.include_router(comment_annotations_router, prefix='/v1', tags=["Comment Annotations"])
        from ai_core.api.transcription_request_api import router as transcription_request_router
        self.app.include_router(transcription_request_router, prefix='/v1', tags=["Transcription Request"])
        from ai_core.api.sql_api import router as sql_router
        self.app.include_router(sql_router, prefix='/v1', tags=["Sql"])
        from ai_core.api.tenant_reports_api import router as tenant_reports_router
        self.app.include_router(tenant_reports_router, prefix='/v1', tags=["Tenant Reports"])
            

        @self.app.exception_handler(Exception)
        async def global_exception_handler(request: Request, exc: Exception):
            log_exception(exc)
            return create_error_response(
                detail=f"An unexpected error occurred: {str(exc)}",
                status_code=500
            )

        @self.app.exception_handler(HTTPException)
        async def http_exception_handler(request: Request, exc: HTTPException):
            log_exception(exc)
            return create_error_response(detail=exc.detail, status_code=exc.status_code)

        @self.app.exception_handler(RequestValidationError)
        async def validation_exception_handler(request: Request, exc: RequestValidationError):
            log_exception(exc)
            return create_error_response(detail="Invalid request data.", status_code=422)

        @self.app.post("/v1/register", description="Create a new registration", response_model=ServiceResponseMessage)
        def create_registration(registration: Registration, request: Request, db: DatabaseAdapter = Depends(self.get_db_provider), q: QueueClient = Depends(self.get_queue)):
            logger.debug(f"--> create registration: {registration}")
            if not registration.email:
                raise ValueError("register create_registration email cannot be empty")
            if not registration.id:
                raise ValueError("register create_registration id cannot be empty")
            if not registration.password:
                raise ValueError("register create_registration password cannot be empty")
            registration.id = registration.id.lower()
            registration.email = registration.email.lower()
            try:
                if db:
                    registration.verification_token = str(uuid.uuid4())
                    registration.id = registration.verification_token
                    registration.password_hash = self.auth.generate_hash(registration.password)
                    registration.password = None
                    registration.created = int(time.time())
                    db.insert_item("registration", registration.id, registration.model_dump())
                    # send_verification_email(self.settings, registration.email, registration.verification_token)
                    send_verification_email(self.settings, registration)

                safe_invoke("ai_core.event_handlers", "on_new_registration", [registration, db, q, request])

            except Exception as e:
                logger.error(f"Error creating registration: {e}")
                raise HTTPException(status_code=500, detail=str(e))
            return info_message("Registration created successfully")

        @self.app.get("/v1/verify/{token}", description="Verify a user's email", response_model=ServiceResponseMessage)
        def verify(token: str, request: Request, db: DatabaseAdapter = Depends(self.get_db_provider)):
            logger.debug(f"--> verify email: {token}")
            try:
                return verify_and_register(token)
            except Exception as e:
                logger.error(f"Error verifying email: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.post("/v1/login", description="Login using JSON POST", response_model=LoginResponse)
        async def login(request: LoginRequest, db: DatabaseAdapter = Depends(self.get_db_provider)):
            """
            Login endpoint that accepts both form and JSON requests.
            """
            try:
                username = request.username
                password = request.password
                
                if not username or not password:
                    raise HTTPException(status_code=401, detail="Invalid credentials")
                
                logger.info(f"Received login request for user: {username}")
                username = username.lower()
                user = db.get_item("user", username)
                if not user:
                    raise HTTPException(status_code=401, detail="Invalid credentials")
                
                token = self.auth.authenticate(username, password)
                if not token:
                    raise HTTPException(status_code=401, detail="Invalid credentials")
                # Generate and store refresh token
                refresh_token = str(uuid.uuid4())
                user["refresh_token"] = refresh_token
                db.update_item("user", user["id"], user)
                if user.get("is_mfa_enabled"):
                    return {"mfa_required": True, "message": "MFA verification required"}
                return {"access_token": token, "token_type": "bearer", "refresh_token": refresh_token}
            
            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"Login error: {str(e)}")
                raise HTTPException(status_code=401, detail="Invalid credentials")

        @self.app.post("/v1/mfa/login")
        def mfa_login(request: MFAVerifyRequest, db: DatabaseAdapter = Depends(self.get_db_provider)):
            try:
                from auth import mfa
                request.username = request.username.lower()
                user = db.get_item("user", request.username)
                if not user or not user.get("totp_secret"):
                    raise HTTPException(status_code=400, detail="MFA not set up")
                totp_secret = user["totp_secret"]
                logger.info(f"Verifying TOTP for user: {request.username}")
                logger.info(f"TOTP Secret: {totp_secret}")
                logger.info(f"Provided Code: {request.code}")
                if not mfa.verify_totp(secret=user.get("totp_secret"), code=request.code, valid_window=5):
                    logger.warning(f"Invalid TOTP code for user: {request.username}")
                    raise HTTPException(status_code=401, detail="Invalid TOTP code")
                token = self.auth.generate_token(user)
                db.update_item("user", user["id"], {"token": token})
                logger.info(f"MFA login successful for user: {request.username}")
                return {"access_token": token, "token_type": "bearer"}
            except ImportError:
                logger.error("MFA module is not available.")
                raise HTTPException(status_code=500, detail="MFA functionality is not enabled.")
            except Exception as e:
                logger.error(f"Error during MFA login: {e}")
                raise HTTPException(status_code=500, detail="Failed to process MFA login")

        @self.app.post("/v1/forgot-password", description="Send a password reset email")
        def forgot_password(request: ForgotPasswordRequest, db: DatabaseAdapter = Depends(self.get_db_provider)):
            logger.debug(f"Received forgot password request: {request.dict()}")
            try:
                user = db.get_item("user", request.email)
                if not user:
                    raise HTTPException(status_code=404, detail="User not found")
                reset_token = str(uuid.uuid4())
                user["reset_token"] = reset_token
                user["reset_token_expiry"] = int(time.time()) + 3600
                db.update_item("user", user["id"], user)
                send_reset_password_email(
                    config=self.settings,
                    user_email=request.email,
                    reset_token=reset_token,
                )
                logger.info(f"Password reset email sent to {request.email}")
            except Exception as e:
                logger.error(f"Error handling forgot password request: {e}")
                raise HTTPException(status_code=500, detail="Failed to process forgot password request")
            return info_message("Password reset email sent successfully")

        @self.app.post("/v1/reset-password", description="Reset the user's password")
        def reset_password(request: ResetPasswordRequest, db: DatabaseAdapter = Depends(self.get_db_provider)):
            logger.debug(f"Received reset password request: {request.dict()}")
            try:
                users = db.query_items("user", {"reset_token": request.resetToken})
                if not users or len(users) == 0:
                    raise HTTPException(status_code=404, detail="Invalid or expired reset token")
                user = users[0]
                if int(time.time()) > user["reset_token_expiry"]:
                    raise HTTPException(status_code=400, detail="Reset token has expired")
                user["password_hash"] = self.auth.generate_hash(request.newPassword)
                user["reset_token"] = None
                user["reset_token_expiry"] = None
                db.update_item("user", user["id"], user)
                logger.info(f"Password reset successfully for user: {user['id']}")
            except Exception as e:
                logger.error(f"Error resetting password: {e}")
                raise HTTPException(status_code=500, detail="Failed to reset password")
            return info_message("Password reset successfully")

        @self.app.post("/v1/refresh-token", response_model=RefreshTokenResponse)
        async def refresh_token(request: RefreshTokenRequest, db: DatabaseAdapter = Depends(self.get_db_provider)):
            try:
                users = db.query_items("user", {"refresh_token": request.refresh_token})
                if not users or len(users) == 0:
                    raise HTTPException(status_code=401, detail="Invalid refresh token")
                user = users[0]
                # Optionally: check expiry, rotate token, etc.
                new_access_token = self.auth.generate_token(user)
                # Optionally rotate refresh token for better security
                new_refresh_token = str(uuid.uuid4())
                user["refresh_token"] = new_refresh_token
                db.update_item("user", user["id"], user)
                return {"access_token": new_access_token, "token_type": "bearer", "refresh_token": new_refresh_token}
            except Exception as e:
                logger.error(f"Error refreshing token: {e}")
                raise HTTPException(status_code=401, detail="Could not refresh token")
            
        @self.app.post("/v1/logout", description="Logout the current user")
        async def logout(request: Request, db: DatabaseAdapter = Depends(self.get_db_provider)):
            try:
                # Extract token from Authorization header
                auth_header = request.headers.get("authorization")
                if not auth_header or not auth_header.lower().startswith("bearer "):
                    raise HTTPException(status_code=401, detail="Missing or invalid authorization header")
                token = auth_header.split(" ", 1)[1]
                # Decode token to get user info
                user = None
                try:
                    payload = self.auth._verify_jwt(token)
                    username = payload.get("sub")
                    if username:
                        user = db.get_item("user", username)
                except Exception:
                    pass
                if not user:
                    raise HTTPException(status_code=401, detail="Invalid token or user not found")
                # Invalidate token and refresh token
                user["token"] = None
                user["refresh_token"] = None
                db.update_item("user", user["id"], user)
                return info_message("Logout successful")
            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"Logout error: {str(e)}")
                raise HTTPException(status_code=500, detail="Logout failed")


        @self.app.get("/{full_path:path}")
        async def serve_react_app(full_path: str):

            # TODO: have config "if it exists" to prevent cache busting here
            headers = {
                "Cache-Control": "no-cache, no-store, must-revalidate",
                "Pragma": "no-cache",
                "Expires": "0"
            }

            file_path = os.path.join("build", full_path)
            if os.path.exists(file_path) and not os.path.isdir(file_path):
                return FileResponse(file_path, headers=headers)
            return FileResponse("build/index.html", headers=headers)

    def get_db_provider(self) -> DatabaseAdapter:
        # TODO add config option for get_database_client(settings.db_name)
        return get_database_client()
        

    def get_queue(self) -> QueueClient:
        queue_type = self.settings.queue_type
        q_params: dict[str, str] = {}
        if not queue_type:
            return None
        if queue_type == "local":
            q_params = {}
        elif queue_type == "sqs":
            q_params = {}
        return get_queue_client(name="config", queue_type="local", **q_params)

    async def run(self):
        """Runs the application asynchronously."""
        import uvicorn
        from uvicorn import Config, Server
        logging.info(f"Version: {version}")
        logging.info(f"Running on port: {self.settings.port}")
        config_kwargs = {
            "app": self.app,
            "host": "0.0.0.0",
            "port": self.settings.port,
            "reload": False
        }
        if self.settings.ssl_enabled:
            logging.info("SSL enabled")
            generate_self_signed_cert()
            config_kwargs["ssl_keyfile"] = "ssl/key.pem"
            config_kwargs["ssl_certfile"] = "ssl/cert.pem"
        config = Config(**config_kwargs)
        server = Server(config)
        await server.serve()

if __name__ == "__main__":
    settings_obj, config_provider = create_app_settings("ai_core", AiCoreConfig)
    settings: AiCoreConfig = settings_obj.to_config()
    logger.info(f"App Setting: {json.dumps(settings.model_dump(), indent=2)}")

    app_server = AppServer(settings)
    import asyncio
    asyncio.run(app_server.run())
