"""
Simplified LangChain RAG Implementation for Question Answering

This is a simplified version that:
1. Uses default LangChain text chunking (RecursiveCharacterTextSplitter)
2. Uses default similarity-based retrieval
3. Removes all fancy chunking strategies (semantic, question-aware, memory-test, etc.)
4. Removes complex question code tagging
5. Keeps model switching logic and basic output document generation
6. Uses simple, straightforward approach for better maintainability
"""

import logging
import openai
from itertools import zip_longest
from concurrent.futures import ThreadPoolExecutor
import json
import jsonschema
from datetime import datetime
import os
import time
import argparse
import uuid
import threading
from config import create_app_settings

from ai_core.models.config import Config as AiCoreConfig

# LangChain imports
from langchain_openai import OpenAIEmbeddings, ChatOpenAI
from langchain_community.vectorstores import Chroma
from langchain_community.embeddings import HuggingFaceEmbeddings
from langchain.chains import RetrievalQA
from langchain.schema import Document
from langchain.prompts import PromptTemplate
from langchain.callbacks.base import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from typing import Any, Dict, List, Optional
from dataclasses import dataclass

# Clinician Observation Configuration
def load_clinician_observation_config():
    """Load clinician observation configuration from JSON file"""
    config_path = os.path.join(os.path.dirname(__file__), "clinician_observation_config.json")
    try:
        with open(config_path, 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        logger.warning(f"Clinician observation config file not found at {config_path}")
        return {"clinician_observation_config": {}}
    except json.JSONDecodeError as e:
        logger.error(f"Error parsing clinician observation config: {e}")
        return {"clinician_observation_config": {}}

def retrieve_clinician_observation_context(rag_system, group_name: str) -> str:
    """
    Retrieve raw conversation context for clinician observations for a specific question group

    Args:
        rag_system: The RAG system instance for retrieval
        group_name: The OASIS group name (e.g., "Functional Abilities", "Cognitive Status")

    Returns:
        Raw retrieved context as string for clinician observations
    """
    config = load_clinician_observation_config()
    group_config = config.get("clinician_observation_config", {}).get(group_name)

    if not group_config or not group_config.get("enabled", False):
        return ""

    search_queries = group_config.get("search_queries", [])

    if not search_queries:
        return ""

    try:
        # Check if vectorstore is available
        if not hasattr(rag_system, 'vectorstore') or rag_system.vectorstore is None:
            logger.warning(f"No vectorstore available for clinician observation context retrieval for {group_name}")
            return ""

        # Retrieve relevant context using the search queries - limit to 2-3 chunks total
        all_retrieved_docs = []
        for query in search_queries:
            retrieved_docs = rag_system.vectorstore.similarity_search(query, k=1)  # Get only 1 chunk per query
            all_retrieved_docs.extend(retrieved_docs)

        if not all_retrieved_docs:
            return ""

        # Limit to top 3 chunks maximum to keep context focused and concise
        limited_docs = all_retrieved_docs[:3]

        # Combine retrieved context - this is the raw context that will be passed as "clinician observations"
        context_text = "\n\n".join([doc.page_content for doc in limited_docs])

        # Log the retrieved context for debugging
        logger.info(f"Retrieved clinician observation context for {group_name}: {len(limited_docs)} chunks, {len(context_text)} characters")

        return context_text

    except Exception as e:
        logger.error(f"Error retrieving clinician observation context for {group_name}: {e}")
        return ""

# OASIS Group Mapping for intelligent batching
OASIS_GROUP_MAP = {
    # Administrative/Patient Tracking
    "A1000–A2300": "Patient Demographics",
    "M0080–M0102": "Assessment Info",

    # Patient History & Diagnosis
    "M1000–M1100": "Patient History",
    "M1028–M1060": "Health Conditions",
    "J0510–J0530": "Pain Assessment",

    # Sensory
    "B0200–B1300": "Vision & Communication",

    # Cognitive/Mental Status
    "C0100–C0500": "Cognitive Status",
    "C1310": "Delirium Assessment",

    # Mood/Behavioral
    "D0150–D0700": "Mood/Behavior",

    # Integumentary (Skin)
    "M1306–M1342": "Skin/Wound Assessment",

    # Respiratory
    "M1400": "Respiratory Status",

    # Elimination
    "M1600–M1630": "Elimination Status",

    # Neuro/Cognitive Function
    "M1700–M1745": "Cognitive Function",

    # ADLs (Activities of Daily Living)
    "M1800–M1870": "ADLs Assessment",



    # Functional Abilities (GG Section) - Split into specific areas
    "GG0100–GG0110": "Prior Functioning",
    "GG0130": "Self-Care",
    "GG0170": "Mobility",

    # Functional Support
    "G0900": "Functional Support",

    # Medications
    "N0415–N2005": "Medications",

    # Risk Assessment
    "M1033": "Risk Assessment",

    # Other assessments that might appear
    "O0110–O0420": "Treatments",
    "Z0400–Z0500": "Clinician Sign-Off"
}

def load_domain_instructions(group_name: str) -> str:
    """
    Load domain-specific instructions based on the group name

    Args:
        group_name: The group name (e.g., "Cognitive Status", "Movement Assessment")

    Returns:
        Domain-specific instructions as string for the specified group
    """
    # Switch case to determine filename and display name based on group name
    if group_name == "Cognitive Status":
        filename = "cognitive_assessment.md"
        display_name = "COGNITIVE ASSESSMENT"
    elif group_name == "ADLs Assessment":
        filename = "ADLs_assessment.md"
        display_name = "ADLs ASSESSMENT"
    elif group_name == "Prior Functioning":
        filename = "prior_functioning.md"  # Reuse existing functional assessment file
        display_name = "PRIOR FUNCTIONING ASSESSMENT"
    elif group_name == "Self-Care":
        filename = "self_care_assesment.md"  # Reuse existing functional assessment file
        display_name = "SELF-CARE ASSESSMENT"
    elif group_name == "Mobility":
        filename = "mobility.md"  # Reuse existing functional assessment file
        display_name = "MOBILITY ASSESSMENT"
    # Add more cases as needed:
    # elif group_name == "ADLs Assessment":
    #     filename = "adl_assessment.md"
    #     display_name = "ADL ASSESSMENT"
    else:
        print(f"⚠️ No domain instructions available for group: {group_name}")
        return ""

    try:
        current_dir = os.path.dirname(os.path.abspath(__file__))
        instruction_path = os.path.join(current_dir, "domain_instructions", filename)

        with open(instruction_path, "r", encoding="utf-8") as file:
            instructions = file.read()
            return f"\n\n## DOMAIN-SPECIFIC INSTRUCTIONS FOR {display_name}:\n{instructions}\n"
    except FileNotFoundError:
        print(f"⚠️ Domain instructions file not found: {filename}")
        return ""
    except Exception as e:
        print(f"⚠️ Error loading domain instructions for {group_name}: {e}")
        return ""

def get_domain_instructions_for_group(group_name: str) -> str:
    """
    Get domain-specific instructions for a given OASIS group

    Args:
        group_name: The OASIS group name (e.g., "Cognitive Status")

    Returns:
        Domain-specific instructions as string, or empty string if not available
    """
    return load_domain_instructions(group_name)

# Configuration Management
@dataclass
class RAGConfig:
    """Configuration constants for RAG system"""
    # Chunking parameters - simplified
    DEFAULT_CHUNK_SIZE: int = 1000
    DEFAULT_CHUNK_OVERLAP: int = 200

    # Retrieval parameters - simplified
    DEFAULT_RETRIEVAL_K: int = 5

    # LLM parameters
    DEFAULT_LLM_MODEL: str = "gpt-5-mini"
    DEFAULT_TEMPERATURE: float = 0.0

    # Embedding parameters
    DEFAULT_EMBEDDING_MODEL: str = "text-embedding-ada-002"

    # File output parameters
    ENABLE_CHUNK_ANALYSIS: bool = True
    ENABLE_READABLE_OUTPUT: bool = True

    # OASIS grouping parameters
    MAX_SUBGROUP_SIZE: int = 5  # Maximum questions per subgroup for better retrieval

    # Domain-specific instructions
    DOMAIN_INSTRUCTIONS_DIR: str = "domain_instructions"

delete_openai_objects = False
number_of_question = 5

logger = logging.getLogger(__name__)

# Configure logging for better error tracking
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Constants - same as original
INSTRUCTIONS = """
You are an expert system designed to extract relevant information/answer from conversations between clinician and patient.
Given a set of questions from the user, you will analyze the transcript and provide concise answers to the user's questions based on the information present in the conversation.
If the question is not found in the conversation, the default answer will be "Not Available"
"""

user_content_default = """
### Objective
{{role_injection}}
You are going to answer Oasis start of care questions
Extract answers from the conversation transcript for each question listed below. Base your responses ONLY on information present in the conversation. Use ["Not Available"] for any question without a clear answer in the transcript.

Today's Date: {{todays_date}}

### Instructions:
1. Extract information ONLY from the provided conversation.
2. For each question, provide the exact relevant information without adding assumptions.
3. Use the format specified in the Output section.
4. Return ["Not Available"] when information cannot be found.
5. Select appropriate options from the provided choices when applicable.
6. Even though context has clinician and patient labels, sometimes the patient's answer will appear within the same turn as the clinician's question. In that case, you must split the turn into two logical parts — the question (clinician) and the answer (patient) — and treat them separately.
7. Use common sense and language cues (e.g., who is asking and who is replying) to identify speaker roles even when the diarization is incorrect.
9. detect who is speaking only based on language and context not from the label. its interchanged sometime, due to poor diarization.
8. Only rely on factual content present in the conversation. Do not assume or infer missing data.
10. **Semantic Understanding**: Responses come from human-clinician conversations. Do NOT rely on exact keyword matches. Use contextual meaning to interpret answers.
11. use domain specific rules provided if available for answering questions
12. rate your confidence for each answer from 0.00 to 1.00,based only on how clearly the conversation evidence supports the answer.
13. use domain specific rules along with clinician observation context to answer questions. The clinician observation context contains relevant conversation segments that may reveal important clinical information (e.g., mobility aids, cognitive status, functional limitations).
14. Analyze the clinician observation context to identify key clinical findings and apply them when answering questions, even if they override patient's direct responses.
15. apply cinician observation only when domain instruction ask you to do so. for example, if domain instruction says, use clinician observation and if clinician observation has this, do this kind of instructions

Rules for confidence scoring:
- 1.00 = Certain beyond reasonable doubt (explicit evidence)
- 0.80–0.99 = Strong evidence but some minor uncertainty
- 0.50–0.79 = Partial evidence, needs interpretation
- 0.00–0.49 = Weak or no evidence


### Input Format
Each question has this format:
- question_code: Unique identifier
- question: The text prompt
- question_type: Format type (radio-group, select-drop-down, checklist, checkbox, date-field, multiline-text-field)
- labelName: Display label
- section: Category
- options: Available choices (when applicable)

### Response Format Rules
- radio-group, select-drop-down, checkbox, multiline-text-field: Single string in answer_text array
- checklist: Can have multiple strings in answer_text array when multiple options apply
- date-field: String in format "MM-DD-YYYY" in answer_text array. if date is in conversation format, twenty three, august, two thousand twenty three, convert it to "08-23-2023", if few informations about date is missing, use todays date as reference to  interpret remaining details like month and year
- All responses must include question_code, question_text, answer_context, answer_reason, and answer_text
- Response  string will be converted to json object, so ensure formatting is correct and no special characters breaks the json structure. 
- The output must parse successfully with a standard JSON parser.
- Do not add extra escape characters.
- use below output schema example, but no new line and spaces are needed. 
- numeric-with-units should contain single value, whenever range of value is detected always answer higher value with unit.
### Output Schema (JSON only)
{
  "question_code": "[ID from input]",
  "question_type": "[Question Type for RPA]",
  "question_text": "[Text of the question]",
  "answer_context": ["[Patient's exact sentence from transcript, if needed add clinician's question before patient's answer and also clinician affirmation after patient's answer]"],
  "answer_reason": ["Explain your reasoning step-by-step, showing how the context and domain-specific rules led you to this answer. never say "domain instruction" instead say coding instructions from oasis"],
  "answer_text": ["[Selected option or extracted answer]"]
  "confidence_score":[confidence score from 0.00 to 1.00 based on reasoning and given context. this should be number not string]
}

### Example
For a radio-group question about feeding ability where the transcript shows "I can feed myself":
{
  "question_code": "M1870",
  "question_type": "radio-group",
  "question_text": "Current ability to feed self meals and snacks safely",
  "answer_context": ["I can feed myself."],
  "answer_reason": ["Chunk 14 contains direct patient speech confirming independent feeding ability."],
  "answer_text": ["0 - Able to independently feed self."],
  "confidence_score": 1.00
}
## Clinician Observation Context

{{clinician_observations}}

Follow this domain specific rules strictly if available for answering questions
{{domain_specific_instructions}}
## Questions to Answer:
{{question_list_in_json}}
"""

# Updated schemas to include answer_reason
response_schema = {
    "type": "array",
    "items": {
        "type": "object",
        "required": ["question_code", "question_text", "question_type", "answer_context", "answer_reason", "answer_text"],
        "properties": {
            "question_code": {"type": "string"},
            "question_text": {"type": "string"},
            "question_type": {"type": "string"},
            "answer_context": {
                "type": "array",
                "items": {"type": "string"}
            },
            "answer_reason": {
                "type": "array",
                "items": {"type": "string"}
            },
            "confidence_score": {
                "type": "number",
                "minimum": 0.0,
                "maximum": 1.0
            },
            "answer_text": {
                "type": "array",
                "items": {"type": "string"}
            }
        },
        "additionalProperties": False
    }
}

single_response_schema = {
    "type": "object",
    "required": ["question_code", "question_text", "question_type", "answer_context", "answer_reason", "answer_text","confidence_score"],
    "properties": {
        "question_code": {"type": "string"},
        "question_text": {"type": "string"},
        "question_type": {"type": "string"},
        "answer_context": {
            "type": "array",
            "items": {"type": "string"}
        },
        "answer_reason": {
            "type": "array",
            "items": {"type": "string"}
        },
        "confidence_score": {
            "type": "number",
            "minimum": 0.0,
            "maximum": 1.0
        },
        "answer_text": {
            "type": "array",
            "items": {"type": "string"}
        }
    },
    "additionalProperties": False
}

class RetrievalDebugHandler(BaseCallbackHandler):
    """Custom callback handler to capture retrieval information"""

    def __init__(self):
        self.retrieved_docs = []
        self.query = ""
        self.llm_input = ""
        self.llm_output = ""

    def on_retriever_start(self, serialized: Dict[str, Any], query: str, **kwargs: Any) -> None:
        """Called when retriever starts"""
        self.query = query

    def on_retriever_end(self, documents: List[Document], **kwargs: Any) -> None:
        """Called when retriever ends"""
        self.retrieved_docs = documents

        # for i, doc in enumerate(documents, 1):
        #     chunk_type = doc.metadata.get('chunk_type', 'unknown')
        #     print(f"\n📋 Retrieved Text Chunk {i} ({chunk_type}):")
        #     print(f"{'-'*50}")
        #     # Clean up the JSON formatting to make it more readable
        #     content = doc.page_content
        #     try:
        #         # Try to parse and reformat if it's JSON
        #         import json
        #         parsed = json.loads(content)
        #         if isinstance(parsed, list):
        #             for j, item in enumerate(parsed):
        #                 if isinstance(item, dict) and 'text' in item:
        #                     speaker = f"Speaker {item.get('speaker_id', 'Unknown')}"
        #                     text = item['text']
        #                     print(f"🗣️  {speaker}: {text}")
        #                 else:
        #                     print(f"📝 Item {j+1}: {item}")
        #         else:
        #             print(content)
        #     except:
        #         # If not JSON, just print as is but try to make it more readable
        #         # Remove excessive JSON formatting characters
        #         clean_content = content.replace('",\n    "', '"\n"').replace('{\n    "', '{"')
        #         print(clean_content)
        #     print(f"{'-'*50}")

    def on_llm_start(self, serialized: Dict[str, Any], prompts: List[str], **kwargs: Any) -> None:
        """Called when LLM starts"""
        if prompts:
            self.llm_input = prompts[0]

    def on_llm_end(self, response, **kwargs: Any) -> None:
        """Called when LLM ends"""
        if hasattr(response, 'generations') and response.generations:
            self.llm_output = response.generations[0][0].text


class LangChainRAG:
    """LangChain-based RAG implementation"""
    
    def __init__(self, embedding_model: str = "text-embedding-ada-002", openai_api_key: str = None):
        """
        Initialize LangChain RAG system
        
        Args:
            embedding_model: OpenAI embedding model to use
            openai_api_key: OpenAI API key
        """
        self.embedding_model = embedding_model
        # self.embedding_model = "emilyalsentzer/Bio_ClinicalBERT"
        self.openai_api_key = openai_api_key
        self.embeddings = None
        self.vectorstore = None
        self.retriever = None
        self.qa_chain = None
        self.debug_handler = RetrievalDebugHandler()
        
        # Initialize embeddings
        self._initialize_embeddings()
    
    def _initialize_embeddings(self):
        """Initialize the embedding model"""
        # OpenAI embedding models
        openai_models = [
            "text-embedding-ada-002",
            "text-embedding-3-small",
            "text-embedding-3-large"
        ]

        # Hugging Face embedding models
        huggingface_models = [
            "emilyalsentzer/Bio_ClinicalBERT",
            "michiyasunaga/BioLinkBERT-large",
            "sentence-transformers/all-MiniLM-L6-v2",
            "sentence-transformers/all-mpnet-base-v2",
            "microsoft/BiomedNLP-PubMedBERT-base-uncased-abstract-fulltext"
        ]

        if self.embedding_model in openai_models:
            self.embeddings = OpenAIEmbeddings(
                model=self.embedding_model,
                openai_api_key=self.openai_api_key
            )
        elif self.embedding_model in huggingface_models:
            self.embeddings = HuggingFaceEmbeddings(
                model_name=self.embedding_model,
                model_kwargs={'device': 'cpu'},  # Use CPU for compatibility
                encode_kwargs={'normalize_embeddings': True}
            )
        else:
            # Auto-detect based on model name pattern
            if self.embedding_model.startswith("text-embedding"):
                self.embeddings = OpenAIEmbeddings(
                    model=self.embedding_model,
                    openai_api_key=self.openai_api_key
                )
            else:
                self.embeddings = HuggingFaceEmbeddings(
                    model_name=self.embedding_model,
                    model_kwargs={'device': 'cpu'},
                    encode_kwargs={'normalize_embeddings': True}
                )
    
    def _create_simple_chunks(self, conversation_data: dict, client_id: str):
        """
        Create simple chunks from conversation data using default LangChain text splitter

        Args:
            conversation_data: The conversation data
            client_id: Client identifier

        Returns:
            List of Document objects with simple chunks
        """
        from langchain.text_splitter import RecursiveCharacterTextSplitter

        # First, clean the conversation using LLM to fix speaker diarization
        # cleaned_conversation_data = self._clean_conversation_with_llm(conversation_data)

        # Save cleaned data to JSON file for evaluation
        # self._save_cleaned_conversation(conversation_data, cleaned_conversation_data, client_id)

        # Convert cleaned conversation data to readable text format
        conversation_text = self._format_conversation_to_text(conversation_data)

        # Create a single document
        document = Document(
            page_content=conversation_text,
            metadata={
                "client_id": client_id,
                "chunk_type": "default_text_split"
            }
        )

        # Use default LangChain text splitter
        text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=RAGConfig.DEFAULT_CHUNK_SIZE,
            chunk_overlap=RAGConfig.DEFAULT_CHUNK_OVERLAP,
            length_function=len,
        )

        # Split the document into chunks
        chunks = text_splitter.split_documents([document])

        # Add chunk index to metadata
        for i, chunk in enumerate(chunks):
            chunk.metadata.update({
                "chunk_index": i,
                "total_chunks": len(chunks)
            })

        return chunks

    def _clean_conversation_with_llm(self, conversation_data: dict) -> dict:
        """
        Use LLM to clean up Deepgram transcription and fix speaker diarization issues

        Args:
            conversation_data: Raw conversation data from Deepgram

        Returns:
            Cleaned conversation data with corrected speaker assignments
        """
        # Handle both list and dict formats
        if isinstance(conversation_data, dict):
            conversation_list = conversation_data.get('conversation', conversation_data.get('data', []))
            if not isinstance(conversation_list, list):
                conversation_list = [conversation_data]
        else:
            conversation_list = conversation_data

        if not conversation_list:
            return conversation_data

        # Prepare the conversation for LLM analysis
        conversation_text = ""
        for i, turn in enumerate(conversation_list):
            if not isinstance(turn, dict) or 'text' not in turn:
                continue

            speaker_id = turn.get('speaker_id', 'unknown')
            text = turn.get('text', '').strip()
            if text:
                conversation_text += f"Turn {i+1} (Original Speaker {speaker_id}): {text}\n"

        if not conversation_text:
            return conversation_data

        # LLM prompt to clean and fix speaker diarization
        prompt = f"""You are an expert in medical conversation analysis. Below is a transcription from Deepgram with speaker diarization that may have errors. Your task is to:

1. Analyze each turn and determine if the speaker assignment is correct
2. Fix any obvious speaker diarization errors (patient labeled as clinician or vice versa note: patient and clinician are note: speaker 0 and speaker 1 are clinician and patient respectively )
3. Look for patterns like:
   - Medical questions/assessments (usually clinician)
   - Personal responses about symptoms/conditions (usually patient)
   - Professional medical terminology (usually clinician)
   - Casual/personal language about health (usually patient)
   - Sometimes, clinician question and patient answer will be in the same turn, in that case, you need to split the turn into two separate turns.
   - 
4. sometime, patient answer will be in the same turn as clinician question, in that case, you need to split the turn into two separate turns.

CONVERSATION TO ANALYZE:
{conversation_text}

INSTRUCTIONS:
- Speaker 0 should be CLINICIAN (healthcare provider)
- Speaker 1 should be PATIENT
- Return ONLY a JSON array with corrected speaker assignments
- Keep the exact same text, only fix speaker_id if needed
- Each object should have: {{"speaker_id": 0 or 1, "text": "exact original text"}}

RESPONSE FORMAT (JSON only):
[
  {{"speaker_id": 0, "text": "original text here"}},
  {{"speaker_id": 1, "text": "original text here"}}
]"""

        try:
            # Use OpenAI to clean the conversation
            llm = ChatOpenAI(
                model=RAGConfig.DEFAULT_LLM_MODEL,
                # temperature=0.0,  # Low temperature for consistent corrections
                openai_api_key=self.openai_api_key
            )

            response = llm.invoke(prompt)
            cleaned_text = response.content.strip()

            # Clean up the response to extract JSON
            if cleaned_text.startswith("```json"):
                cleaned_text = cleaned_text.replace("```json", "").replace("```", "").strip()
            elif cleaned_text.startswith("```"):
                cleaned_text = cleaned_text.replace("```", "").strip()

            # Parse the cleaned conversation
            try:
                cleaned_conversation = json.loads(cleaned_text)
                if isinstance(cleaned_conversation, list):
                    # Return in the same format as input
                    if isinstance(conversation_data, dict):
                        result = conversation_data.copy()
                        if 'conversation' in result:
                            result['conversation'] = cleaned_conversation
                        elif 'data' in result:
                            result['data'] = cleaned_conversation
                        else:
                            result = cleaned_conversation
                        return result
                    else:
                        return cleaned_conversation
                else:
                    print("⚠️ LLM response was not a list, using original data")
                    return conversation_data

            except json.JSONDecodeError as e:
                print(f"⚠️ Failed to parse LLM response as JSON: {e}")
                print(f"LLM Response: {cleaned_text[:200]}...")

                # Try to fix the JSON using LLM
                print("🔧 Attempting to fix JSON using LLM...")
                try:
                    fixed_json_string = fix_json_with_llm(cleaned_text, str(e), self.openai_api_key)
                    cleaned_conversation = json.loads(fixed_json_string)
                    print("✅ Successfully parsed LLM-fixed JSON")

                    if isinstance(cleaned_conversation, list):
                        # Return in the same format as input
                        if isinstance(conversation_data, dict):
                            result = conversation_data.copy()
                            if 'conversation' in result:
                                result['conversation'] = cleaned_conversation
                            elif 'data' in result:
                                result['data'] = cleaned_conversation
                            else:
                                result = cleaned_conversation
                            return result
                        else:
                            return cleaned_conversation
                    else:
                        print("⚠️ LLM-fixed response was not a list, using original data")
                        return conversation_data

                except (json.JSONDecodeError, Exception) as retry_error:
                    print(f"⚠️ LLM-fixed JSON still has errors: {retry_error}")
                    return conversation_data

        except Exception as e:
            print(f"⚠️ Error during LLM conversation cleaning: {e}")
            return conversation_data

    def _save_cleaned_conversation(self, original_data: dict, cleaned_data: dict, client_id: str):
        """
        Save both original and cleaned conversation data to JSON files for evaluation

        Args:
            original_data: Original conversation data from Deepgram
            cleaned_data: LLM-cleaned conversation data
            client_id: Client identifier for filename
        """
        try:
            timestamp = int(time.time())

            # Save comparison data
            comparison_data = {
                "client_id": client_id,
                "timestamp": timestamp,
                "cleaning_date": time.strftime('%Y-%m-%d %H:%M:%S'),
                "original_conversation": original_data,
                "cleaned_conversation": cleaned_data,
                "summary": {
                    "original_turns": len(original_data.get('conversation', original_data.get('data', []))) if isinstance(original_data, dict) else len(original_data) if isinstance(original_data, list) else 0,
                    "cleaned_turns": len(cleaned_data.get('conversation', cleaned_data.get('data', []))) if isinstance(cleaned_data, dict) else len(cleaned_data) if isinstance(cleaned_data, list) else 0
                }
            }

            # Create filename
            filename = f"conversation_cleaning_comparison_{client_id}_{timestamp}.json"

            # Save to file
            with open(filename, "w", encoding="utf-8") as file:
                json.dump(comparison_data, file, indent=2, ensure_ascii=False)

            # Also create a readable side-by-side comparison
            readable_filename = f"conversation_cleaning_readable_{client_id}_{timestamp}.txt"
            self._create_readable_comparison(original_data, cleaned_data, readable_filename)

        except Exception as e:
            print(f"⚠️ Error saving cleaned conversation: {e}")

    def _create_readable_comparison(self, original_data: dict, cleaned_data: dict, filename: str):
        """
        Create a human-readable side-by-side comparison of original vs cleaned conversation

        Args:
            original_data: Original conversation data
            cleaned_data: Cleaned conversation data
            filename: Output filename
        """
        try:
            # Extract conversation lists
            if isinstance(original_data, dict):
                original_list = original_data.get('conversation', original_data.get('data', []))
            else:
                original_list = original_data if isinstance(original_data, list) else []

            if isinstance(cleaned_data, dict):
                cleaned_list = cleaned_data.get('conversation', cleaned_data.get('data', []))
            else:
                cleaned_list = cleaned_data if isinstance(cleaned_data, list) else []

            with open(filename, "w", encoding="utf-8") as file:
                file.write("=" * 100 + "\n")
                file.write("CONVERSATION CLEANING COMPARISON\n")
                file.write("=" * 100 + "\n\n")

                file.write(f"📊 Original turns: {len(original_list)}\n")
                file.write(f"📊 Cleaned turns: {len(cleaned_list)}\n")
                file.write(f"📅 Processed: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")

                # Compare turn by turn
                max_turns = max(len(original_list), len(cleaned_list))

                for i in range(max_turns):
                    file.write(f"{'='*50} TURN {i+1} {'='*50}\n")

                    # Original turn
                    if i < len(original_list):
                        orig_turn = original_list[i]
                        orig_speaker = orig_turn.get('speaker_id', 'unknown')
                        orig_text = orig_turn.get('text', '')
                        orig_label = "CLINICIAN" if orig_speaker == 0 else "PATIENT" if orig_speaker == 1 else f"SPEAKER_{orig_speaker}"

                        file.write(f"🔴 ORIGINAL: Speaker {orig_speaker} ({orig_label})\n")
                        file.write(f"   Text: {orig_text}\n\n")
                    else:
                        file.write(f"🔴 ORIGINAL: (No turn {i+1})\n\n")

                    # Cleaned turn
                    if i < len(cleaned_list):
                        clean_turn = cleaned_list[i]
                        clean_speaker = clean_turn.get('speaker_id', 'unknown')
                        clean_text = clean_turn.get('text', '')
                        clean_label = "CLINICIAN" if clean_speaker == 0 else "PATIENT" if clean_speaker == 1 else f"SPEAKER_{clean_speaker}"

                        file.write(f"🟢 CLEANED:  Speaker {clean_speaker} ({clean_label})\n")
                        file.write(f"   Text: {clean_text}\n\n")

                        # Check if speaker changed
                        if i < len(original_list):
                            orig_speaker_id = original_list[i].get('speaker_id', 'unknown')
                            if orig_speaker_id != clean_speaker:
                                file.write(f"⚠️  SPEAKER CHANGED: {orig_speaker_id} → {clean_speaker}\n\n")
                    else:
                        file.write(f"🟢 CLEANED:  (No turn {i+1})\n\n")

                    file.write("-" * 100 + "\n\n")

                # Summary of changes
                changes = 0
                if len(original_list) == len(cleaned_list):
                    for i in range(len(original_list)):
                        if (original_list[i].get('speaker_id') != cleaned_list[i].get('speaker_id')):
                            changes += 1

                file.write(f"📈 SUMMARY:\n")
                file.write(f"   Total speaker changes made: {changes}\n")
                file.write(f"   Percentage of turns changed: {(changes/len(original_list)*100):.1f}%\n" if original_list else "   No turns to compare\n")

        except Exception as e:
            print(f"⚠️ Error creating readable comparison: {e}")

    def _format_conversation_to_text(self, conversation_data: dict) -> str:
        """
        Convert conversation data to readable text format

        Args:
            conversation_data: The conversation data

        Returns:
            Formatted conversation text
        """
        # Handle both list and dict formats
        if isinstance(conversation_data, dict):
            conversation_list = conversation_data.get('conversation', conversation_data.get('data', []))
            if not isinstance(conversation_list, list):
                conversation_list = [conversation_data]
        else:
            conversation_list = conversation_data

        formatted_lines = []
        for turn in conversation_list:
            if not isinstance(turn, dict) or 'text' not in turn:
                continue

            speaker_id = turn.get('speaker_id', 'unknown')
            text = turn.get('text', '')

            # Use Speaker 0/Speaker 1 format - clear structure without assumptions
            # LLM can learn patterns: Speaker 0 typically asks, Speaker 1 typically responds
            if text.strip():
                formatted_turn = f"Speaker {speaker_id}: {text.strip()}"
                formatted_lines.append(formatted_turn)

        return "\n".join(formatted_lines)







    def create_vectorstore_from_conversation(self, conversation_data: dict, client_id: str, questions: list = None):
        """
        Create vector store from conversation data using simple default chunking

        Args:
            conversation_data: The conversation data to vectorize
            client_id: Client identifier for the vector store
            questions: Optional list of OASIS questions (kept for compatibility)
        """
        # Create simple chunks using default LangChain text splitter
        all_chunks = self._create_simple_chunks(conversation_data, client_id)

        # Create vector store using Chroma (in-memory)
        self.vectorstore = Chroma.from_documents(
            documents=all_chunks,
            embedding=self.embeddings,
            collection_name=f"conversation_{client_id}_{int(time.time())}"
        )

        # Create retriever with default similarity search
        self.retriever = self.vectorstore.as_retriever(
            search_type="similarity",  # Use simple similarity search
            search_kwargs={
                "k": RAGConfig.DEFAULT_RETRIEVAL_K  # Retrieve default number of chunks
            }
        )

        return self.vectorstore





    def setup_qa_chain(self, llm_model: str = None, temperature: float = None):
        """
        Setup the QA chain with the retriever

        Args:
            llm_model: The LLM model to use (uses config default if None)
            temperature: Temperature for the LLM (uses config default if None)
        """
        if llm_model is None:
            llm_model = RAGConfig.DEFAULT_LLM_MODEL
        if temperature is None:
            temperature = RAGConfig.DEFAULT_TEMPERATURE

        if not self.retriever:
            raise ValueError("Retriever not initialized. Call create_vectorstore_from_conversation first.")

        # Initialize LLM
        llm = ChatOpenAI(
            model=llm_model,
            # temperature=temperature,
            openai_api_key=self.openai_api_key
        )

        # Create custom prompt template
        prompt_template = PromptTemplate(
            input_variables=["context", "question"],
            template="""Use the following pieces of context to answer the question. If you don't know the answer based on the context, just say "Not Available".

Context: {context}

Question: {question}

Answer:"""
        )

        # Create QA chain
        self.qa_chain = RetrievalQA.from_chain_type(
            llm=llm,
            chain_type="stuff",
            retriever=self.retriever,
            chain_type_kwargs={"prompt": prompt_template},
            callbacks=[self.debug_handler],
            return_source_documents=True
        )
    


    def _extract_question_texts_from_prompt(self, full_prompt: str) -> str:
        """
        Extract question texts from the full prompt and create a focused retrieval query

        Args:
            full_prompt: The complete prompt with questions in JSON format

        Returns:
            Focused retrieval query string
        """
        try:
            # Look for JSON content in the prompt
            if "## Questions to Answer:" in full_prompt:
                json_part = full_prompt.split("## Questions to Answer:")[1].strip()
                questions = json.loads(json_part)

                question_texts = []
                if isinstance(questions, list):
                    for q in questions:
                        if isinstance(q, dict) and "question" in q:
                            question_text = q["question"]
                            # Clean up the question text
                            if question_text and question_text.strip():
                                question_texts.append(question_text.strip())

                if question_texts:
                    # Create focused retrieval query
                    retrieval_query = "Find conversation parts where the patient or caregiver talks about:\n"
                    for q_text in question_texts:
                        retrieval_query += f"- {q_text}\n"

                    return retrieval_query

        except (json.JSONDecodeError, KeyError, IndexError) as e:
            logger.warning(f"Could not extract questions from prompt: {e}")

        # Fallback to original prompt if extraction fails
        return full_prompt

    def query(self, question: str) -> dict:
        """
        Query the RAG system using focused retrieval

        Args:
            question: The question to ask (full prompt with questions)

        Returns:
            Dictionary containing the answer and source documents
        """
        if not self.qa_chain:
            raise ValueError("QA chain not initialized. Call setup_qa_chain first.")

        # Reset debug handler for this query
        self.debug_handler = RetrievalDebugHandler()

        # Extract focused retrieval query from the full prompt
        focused_retrieval_query = self._extract_question_texts_from_prompt(question)

        # Use the retriever directly with focused query, then pass full prompt to LLM
        try:
            # Get relevant documents using focused query
            relevant_docs = self.retriever.invoke(focused_retrieval_query)

            # Create context from retrieved documents
            context = "\n\n".join([doc.page_content for doc in relevant_docs])

            # Use LLM directly with full prompt and retrieved context
            llm = ChatOpenAI(
                model=RAGConfig.DEFAULT_LLM_MODEL,
                # temperature=RAGConfig.DEFAULT_TEMPERATURE,
                openai_api_key=self.openai_api_key
            )

            # Create the final prompt with context and original question
            final_prompt = f"""Use the following pieces of context to answer the question. If you don't know the answer based on the context, just say "Not Available".
            answer format should be json array of answers

Context: {context}

Question: {question}

Answer:"""

            response = llm.invoke(final_prompt)
            result_text = response.content

            return {
                "answer": result_text,
                "source_documents": relevant_docs,
                "retrieved_context": [doc.page_content for doc in relevant_docs],
                "retrieved_chunks_detailed": [
                    {
                        "chunk_index": i+1,
                        "content": doc.page_content,
                        "metadata": doc.metadata if hasattr(doc, 'metadata') else {}
                    }
                    for i, doc in enumerate(relevant_docs)
                ],
                "query": question,
                "focused_retrieval_query": focused_retrieval_query,
                "llm_input": final_prompt,
                "llm_output": result_text
            }

        except Exception as e:
            logger.error(f"Error during LLM query processing: {e}", exc_info=True)
            # Return error response
            return {
                "answer": "Error: Unable to process the query due to an internal error.",
                "source_documents": [],
                "retrieved_context": [],
                "retrieved_chunks_detailed": [],
                "query": question,
                "focused_retrieval_query": focused_retrieval_query if 'focused_retrieval_query' in locals() else question,
                "llm_input": question,
                "llm_output": f"Error: {str(e)}",
                "error": str(e)
            }
    
    def cleanup(self):
        """Clean up resources"""
        if self.vectorstore:
            try:
                # Chroma cleanup if needed
                pass
            except Exception as e:
                logger.warning(f"Error during cleanup: {e}")


def get_llm_response_langchain(rag_system: LangChainRAG, user_content: str) -> dict:
    """
    Get LLM response using LangChain RAG system

    Args:
        rag_system: The initialized LangChain RAG system
        user_content: The user query/content

    Returns:
        Dictionary containing the LLM response and retrieval information
    """
    try:
        result = rag_system.query(user_content)
        return {
            "answer": result["answer"],
            "retrieval_info": {
                "retrieved_chunks": result["retrieved_chunks_detailed"],
                "query": result["query"],
                "focused_retrieval_query": result.get("focused_retrieval_query", result["query"]),  # Add retrieval input
                "retrieval_input": result.get("focused_retrieval_query", result["query"]),  # Alternative key name
                "llm_input": result["llm_input"],
                "llm_output": result["llm_output"]
            }
        }
    except Exception as e:
        logger.error(f"Error getting LLM response: {e}")
        raise e


def format_json_response_using_llm(raw_string: str, openai_api_key: str = None):
    """
    Convert to structured JSON format from string using LLM
    Enhanced with JSON error handling and fixing
    """
    try:
        response = openai.ChatCompletion.create(
            model="gpt-4-turbo",
            response_format={ "type": "json_object" },
            messages=[
                {
                    "role": "system",
                    "content": """
    You are a helpful assistant designed to output JSON from raw string with below format :

    ### OUTPUT FORMAT :
    ```
    "response" : {
        [
            {
                "question_code": <question_code>,
                "question_text": <question_text>,
                "question_type": <question_type>,
                "answer_context": <answer_context>,
                "answer_text": <answer_text>,
                "answer_code": <answer_code>
            },
            {
                "question_code": <question_code>,
                "question_text": <question_text>,
                "question_type": <question_type>,
                "answer_context": <answer_context>,
                "answer_text": <answer_text>,
                "answer_code": <answer_code>
            }
        ]
    }
    ```
    """
                },
                {"role": "user", "content": raw_string}
            ]
        )

        response_content = response.choices[0].message.content

        try:
            json_data = json.loads(response_content)
        except json.JSONDecodeError as json_error:
            logger.warning(f"JSON decode error in format_json_response_using_llm: {json_error}")

            if openai_api_key:
                logger.info("Attempting to fix JSON using LLM...")
                fixed_json_string = fix_json_with_llm(response_content, str(json_error), openai_api_key)
                try:
                    json_data = json.loads(fixed_json_string)
                    logger.info("Successfully parsed LLM-fixed JSON in format_json_response_using_llm")
                except json.JSONDecodeError as retry_error:
                    logger.error(f"LLM-fixed JSON still has errors in format_json_response_using_llm: {retry_error}")
                    raise json_error
            else:
                logger.warning("No OpenAI API key provided for JSON fixing in format_json_response_using_llm")
                raise json_error

        if "response" in json_data.keys():
            return json_data.get("response")

        elif "result" in json_data.keys():
            return json_data.get("result")

        elif "question_data" in json_data.keys():
            return json_data.get("question_data")

        else:
            return json_data

    except Exception as e:
        raise e


def get_oasis_group(question_code: str) -> str:
    """
    Determine which OASIS group a question code belongs to

    Args:
        question_code: The OASIS question code (e.g., "A1005", "M0090", "C0200")

    Returns:
        Group name or "Unknown" if not found
    """
    if not question_code:
        return "Unknown"

    # Extract the prefix and number for matching
    code = question_code.upper().strip()

    # Check each group range
    for group_range, group_name in OASIS_GROUP_MAP.items():
        if "–" in group_range:
            # Handle ranges like "A0100–A2300" or "GG0100–GG0170"
            try:
                start_code, end_code = group_range.split("–")

                # Extract prefix and number from range codes
                if start_code.startswith("GG"):
                    start_prefix = "GG"
                    start_num = int(start_code[2:])
                    end_prefix = "GG"
                    end_num = int(end_code[2:])
                else:
                    start_prefix = start_code[0]
                    start_num = int(''.join(filter(str.isdigit, start_code)))
                    end_prefix = end_code[0]
                    end_num = int(''.join(filter(str.isdigit, end_code)))

                # Extract prefix and number from question code
                if code.startswith("GG"):
                    q_prefix = "GG"
                    # Extract number part after GG, handling codes like "GG0130.A"
                    number_part = code[2:].split('.')[0]  # Remove any suffix like ".A"
                    q_num = int(number_part)
                else:
                    q_prefix = code[0]
                    # Extract all digits from the code
                    digits = ''.join(filter(str.isdigit, code))
                    if digits:
                        q_num = int(digits)
                    else:
                        continue

                # Check if code falls within range
                if start_prefix == end_prefix == q_prefix:
                    if start_num <= q_num <= end_num:
                        return group_name

            except (ValueError, IndexError):
                continue
        else:
            # Handle single codes like "M1033"
            if code.startswith(group_range):
                return group_name

    return "Unknown"

def split_assessment_list_by_oasis_groups(assessment_list, max_subgroup_size=10):
    """
    Split assessment list by OASIS groups with subgrouping for large groups

    Args:
        assessment_list: List of question dictionaries
        max_subgroup_size: Maximum number of questions per subgroup (default: 10)

    Returns:
        List of question groups organized by OASIS categories, with large groups split into subgroups
    """
    # Group questions by OASIS category
    groups = {}

    for question in assessment_list:
        question_code = question.get('question_code', '')
        group_name = get_oasis_group(question_code)

        if group_name not in groups:
            groups[group_name] = []
        groups[group_name].append(question)

    # Convert to list of groups, maintaining logical order
    group_order = [
        "Patient Demographics",
        "Assessment Info",
        "Patient History",
        "Health Conditions",
        "Pain Assessment",
        "Vision & Communication",
        "Cognitive Status",
        "Delirium Assessment",
        "Mood/Behavior",
        "Skin/Wound Assessment",
        "Respiratory Status",
        "Elimination Status",
        "Cognitive Function",
        "ADLs Assessment",
        "Prior Functioning",
        "Self-Care",
        "Mobility",
        "Functional Support",
        "Medications",
        "Risk Assessment",
        "Treatments",
        "Clinician Sign-Off",
        "Unknown"
    ]

    result = []
    for group_name in group_order:
        if group_name in groups and groups[group_name]:
            group_questions = groups[group_name]

            # If group has more than max_subgroup_size questions, split into subgroups
            if len(group_questions) > max_subgroup_size:
                subgroups = [group_questions[i:i + max_subgroup_size]
                            for i in range(0, len(group_questions), max_subgroup_size)]

                for subgroup in subgroups:
                    result.append(subgroup)
            else:
                # Group is small enough, keep as single group
                result.append(group_questions)

    return result

# Helper functions - same as original
def split_assessment_list(assessment_list, chunk_size):
    """Splits a list into chunks of a given size."""
    return [assessment_list[i:i + chunk_size] for i in range(0, len(assessment_list), chunk_size)]

def prepare_user_content(splitted_list, template, rag_system=None):
    """Prepares user content by replacing placeholders in the template with domain-specific instructions and clinician observations."""
    user_content_list = []
    for item in splitted_list:
        # Determine the OASIS group for this batch of questions
        domain_instructions = ""
        clinician_observations = ""

        if item:  # Check if the item list is not empty
            # Get the group for the first question in the batch (all questions in a batch should be from same group)
            first_question = item[0]
            question_code = first_question.get('question_code', '')
            group_name = get_oasis_group(question_code)

            # Load domain-specific instructions for this group using the new function
            domain_instructions = get_domain_instructions_for_group(group_name)

            # Retrieve raw context for clinician observations if RAG system is available
            if rag_system:
                clinician_observations = retrieve_clinician_observation_context(rag_system, group_name)
                if not clinician_observations:
                    clinician_observations = "No specific clinician observation context available for this assessment."

        # Prepare the base instructions with domain-specific additions
        user_content_copy = template.replace("{{role_injection}}", INSTRUCTIONS)
        user_content_copy = user_content_copy.replace("{{question_list_in_json}}", json.dumps(item))
        user_content_copy = user_content_copy.replace("{{todays_date - 1 year}}", str(datetime.now().strftime("%m/%d/%Y"))[:10])
        user_content_copy = user_content_copy.replace("{{todays_date}}", str(datetime.now().strftime("%m/%d/%Y"))[:10])
        user_content_copy = user_content_copy.replace("{{domain_specific_instructions}}", domain_instructions)
        user_content_copy = user_content_copy.replace("{{clinician_observations}}", clinician_observations)
        user_content_list.append(user_content_copy)
    return user_content_list

def save_human_readable_retrieval(retrieval_data_list, embedding_model, client_id, assessment_id):
    """
    Save retrieval chunks in human-readable format
    """
    model_name = embedding_model.replace("-", "_").replace(":", "_").replace("/", "_")
    # Use high precision timestamp + unique ID to avoid collisions in multi-threading
    timestamp = f"{time.time():.6f}".replace(".", "_")
    unique_id = str(uuid.uuid4())[:8]
    thread_id = threading.current_thread().ident
    readable_filename = f"retrieval_readable_{model_name}_{client_id}_{timestamp}_{unique_id}_{thread_id}.txt"

    with open(readable_filename, "w", encoding="utf-8") as file:
        file.write("=" * 80 + "\n")
        file.write("RETRIEVAL ANALYSIS REPORT\n")
        file.write("=" * 80 + "\n\n")

        file.write(f"📊 Embedding Model: {embedding_model}\n")
        file.write(f"👤 Client ID: {client_id}\n")
        file.write(f"📋 Assessment ID: {assessment_id}\n")
        file.write(f"📅 Generated: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        file.write(f"🔍 Total Queries: {len(retrieval_data_list)}\n\n")

        for query_idx, retrieval_data in enumerate(retrieval_data_list, 1):
            retrieval_info = retrieval_data["retrieval_info"]

            file.write("=" * 80 + "\n")
            file.write(f"QUERY {query_idx}\n")
            file.write("=" * 80 + "\n\n")

            # Show what was asked
            file.write("🤔 QUESTIONS ASKED:\n")
            file.write("-" * 50 + "\n")
            query_text = retrieval_info["query"]
            # Extract just the questions part
            if "## Questions to Answer:" in query_text:
                questions_part = query_text.split("## Questions to Answer:")[1].strip()
                try:
                    import json
                    questions = json.loads(questions_part)
                    for i, q in enumerate(questions, 1):
                        file.write(f"{i}. {q.get('question', 'N/A')}\n\n")
                except:
                    file.write("Could not parse questions\n\n")

            # Add retrieval input section
            file.write("🎯 RETRIEVAL INPUT (What was sent to retriever):\n")
            file.write("-" * 50 + "\n")
            retrieval_input = retrieval_info.get("retrieval_input", retrieval_info.get("focused_retrieval_query", "Not available"))
            file.write(f"{retrieval_input}\n\n")

            # Show retrieved chunks in readable format
            file.write("📄 RETRIEVED CONVERSATION CHUNKS:\n")
            file.write("-" * 50 + "\n")

            for chunk in retrieval_info["retrieved_chunks"]:
                chunk_metadata = chunk.get('metadata', {})
                chunk_type = chunk_metadata.get('chunk_type', 'unknown')

                # Get question codes assigned by LLM
                question_codes = chunk.get('question_codes', [])
                if not question_codes:
                    # Try to extract from metadata using our helper method format
                    original_codes_str = chunk_metadata.get('_original_question_codes', '')
                    if original_codes_str:
                        question_codes = [code.strip() for code in original_codes_str.split(',') if code.strip()]
                    else:
                        # Fallback to regular question_codes string format
                        question_codes_str = chunk_metadata.get('question_codes', '')
                        question_codes = [code.strip() for code in question_codes_str.split(',') if code.strip()]

                # Get temporal information
                conversation_order = chunk_metadata.get('conversation_order', 'N/A')
                temporal_position = chunk_metadata.get('temporal_position', 'N/A')
                relative_timing = chunk_metadata.get('relative_timing', 'unknown')
                start_turn = chunk_metadata.get('start_turn_index', 'N/A')
                end_turn = chunk_metadata.get('end_turn_index', 'N/A')

                file.write(f"\n📋 Chunk {chunk['chunk_index']} ({chunk_type}) - 🕐 Order: {conversation_order}:\n")

                # Add temporal information
                file.write(f"⏰ Timing: {temporal_position}% through conversation ({relative_timing})\n")
                if start_turn != 'N/A' and end_turn != 'N/A':
                    file.write(f"📍 Conversation Turns: {start_turn}-{end_turn}\n")

                # Add question codes information
                if question_codes:
                    file.write(f"🏷️  OASIS Question Codes: {', '.join(question_codes)}\n")
                    file.write(f"📊 Total Codes Assigned: {len(question_codes)}\n")
                else:
                    file.write(f"🏷️  OASIS Question Codes: None assigned\n")

                file.write("." * 40 + "\n")

                # Parse and format the conversation content based on chunk type
                content = chunk["content"]
                try:
                    # Check if this is a semantic chunk (already formatted)
                    if 'CLINICIAN:' in content or 'PATIENT:' in content:
                        # Semantic chunks are already formatted with CLINICIAN/PATIENT labels
                        lines = content.split('\n')
                        for line in lines:
                            line = line.strip()
                            if line:
                                if line.startswith('CLINICIAN:'):
                                    file.write(f"🩺 {line}\n")
                                elif line.startswith('PATIENT:'):
                                    file.write(f"👤 {line}\n")
                                else:
                                    file.write(f"💬 {line}\n")
                    else:
                        # Blind chunks need JSON parsing
                        lines = content.split('\n')
                        for line in lines:
                            line = line.strip()
                            if line and '"text":' in line:
                                # Extract the text content
                                text_start = line.find('"text": "') + 9
                                text_end = line.rfind('"')
                                if text_start > 8 and text_end > text_start:
                                    text = line[text_start:text_end]
                                    file.write(f"💬 {text}\n")
                            elif line and '"speaker_id":' in line:
                                # Extract speaker info
                                if '"speaker_id": 0' in line:
                                    file.write("\n🩺 CLINICIAN:\n")
                                elif '"speaker_id": 1' in line:
                                    file.write("\n👤 PATIENT:\n")

                        # If no JSON structure found, show raw content (truncated)
                        if not any('"text":' in line for line in lines):
                            file.write(content[:500] + "...\n" if len(content) > 500 else content + "\n")

                except:
                    # Fallback to raw content if parsing fails
                    file.write(content[:500] + "...\n" if len(content) > 500 else content + "\n")

                file.write("." * 40 + "\n")

            # Show LLM response
            file.write(f"\n🤖 LLM RESPONSE:\n")
            file.write("-" * 50 + "\n")
            file.write(retrieval_info["llm_output"] + "\n\n")

    return readable_filename


def save_chunk_analysis_report(vectorstore, embedding_model, client_id, assessment_id, questions):
    """
    Save a comprehensive analysis report of all chunks with their question codes

    Args:
        vectorstore: The Chroma vector store containing all chunks
        embedding_model: The embedding model used
        client_id: Client identifier
        assessment_id: Assessment identifier
        questions: List of OASIS questions for reference
    """
    model_name = embedding_model.replace("-", "_").replace(":", "_").replace("/", "_")
    # Use high precision timestamp + unique ID to avoid collisions in multi-threading
    timestamp = f"{time.time():.6f}".replace(".", "_")
    unique_id = str(uuid.uuid4())[:8]
    thread_id = threading.current_thread().ident
    analysis_filename = f"chunk_analysis_{model_name}_{client_id}_{timestamp}_{unique_id}_{thread_id}.txt"

    # Create a mapping of question codes to question details for reference
    question_map = {}
    for q in questions:
        code = q.get('question_code', '')
        if code:
            question_map[code] = {
                'question': q.get('question', ''),
                'labelName': q.get('labelName', ''),
                'section': q.get('section', '')
            }

    try:
        # Get all documents from the vector store
        all_data = vectorstore.get()
        documents = all_data.get('documents', [])
        metadatas = all_data.get('metadatas', [])

        with open(analysis_filename, "w", encoding="utf-8") as file:
            file.write("=" * 80 + "\n")
            file.write("COMPREHENSIVE CHUNK ANALYSIS REPORT\n")
            file.write("=" * 80 + "\n\n")

            file.write(f"📊 Embedding Model: {embedding_model}\n")
            file.write(f"👤 Client ID: {client_id}\n")
            file.write(f"📋 Assessment ID: {assessment_id}\n")
            file.write(f"📅 Generated: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            file.write(f"📄 Total Chunks: {len(documents)}\n")
            file.write(f"❓ Total OASIS Questions: {len(questions)}\n\n")

            # Calculate statistics
            def get_question_codes_from_metadata(metadata):
                if not metadata:
                    return []
                # Try original codes first
                original_codes_str = metadata.get('_original_question_codes', '')
                if original_codes_str:
                    return [code.strip() for code in original_codes_str.split(',') if code.strip()]
                # Fallback to regular format
                question_codes_str = metadata.get('question_codes', '')
                if question_codes_str:
                    return [code.strip() for code in question_codes_str.split(',') if code.strip()]
                return []

            tagged_chunks = sum(1 for m in metadatas if m and get_question_codes_from_metadata(m))
            total_tags = sum(len(get_question_codes_from_metadata(m)) for m in metadatas if m)

            file.write("📈 TAGGING STATISTICS:\n")
            file.write("-" * 50 + "\n")
            file.write(f"🏷️  Tagged Chunks: {tagged_chunks}/{len(documents)} ({tagged_chunks/len(documents)*100:.1f}%)\n")
            file.write(f"🔢 Total Question Code Assignments: {total_tags}\n")
            file.write(f"📊 Average Tags per Chunk: {total_tags/len(documents):.1f}\n")
            file.write(f"📊 Average Tags per Tagged Chunk: {total_tags/tagged_chunks:.1f}\n\n" if tagged_chunks > 0 else "\n")

            # Show question code distribution
            code_counts = {}
            for metadata in metadatas:
                if metadata:
                    codes = get_question_codes_from_metadata(metadata)
                    for code in codes:
                        code_counts[code] = code_counts.get(code, 0) + 1

            file.write("📋 QUESTION CODE DISTRIBUTION:\n")
            file.write("-" * 50 + "\n")
            for code, count in sorted(code_counts.items()):
                question_info = question_map.get(code, {})
                label = question_info.get('labelName', 'Unknown')
                section = question_info.get('section', 'Unknown')
                file.write(f"{code} ({label} - {section}): {count} chunks\n")
            file.write("\n")

            # Show chunk type distribution
            chunk_type_counts = {}
            for metadata in metadatas:
                if metadata:
                    chunk_type = metadata.get('chunk_type', 'unknown')
                    chunk_type_counts[chunk_type] = chunk_type_counts.get(chunk_type, 0) + 1

            file.write("📊 CHUNK TYPE DISTRIBUTION:\n")
            file.write("-" * 50 + "\n")
            for chunk_type, count in sorted(chunk_type_counts.items()):
                file.write(f"{chunk_type}: {count} chunks\n")
            file.write("\n")

            # Show detailed chunk analysis
            file.write("📄 DETAILED CHUNK ANALYSIS:\n")
            file.write("=" * 80 + "\n\n")

            for i, (doc_content, metadata) in enumerate(zip(documents, metadatas), 1):
                if not metadata:
                    continue

                chunk_type = metadata.get('chunk_type', 'unknown')
                question_codes = get_question_codes_from_metadata(metadata)
                turn_count = metadata.get('turn_count', 'N/A')

                # Get temporal information
                conversation_order = metadata.get('conversation_order', 'N/A')
                temporal_position = metadata.get('temporal_position', 'N/A')
                relative_timing = metadata.get('relative_timing', 'unknown')
                start_turn = metadata.get('start_turn_index', 'N/A')
                end_turn = metadata.get('end_turn_index', 'N/A')

                file.write(f"📋 CHUNK {i} ({chunk_type}) - 🕐 Order: {conversation_order}:\n")
                file.write("-" * 60 + "\n")

                # Chunk metadata
                file.write(f"🔢 Turn Count: {turn_count}\n")
                file.write(f"📏 Content Length: {len(doc_content)} characters\n")

                # Temporal information
                file.write(f"⏰ Temporal Position: {temporal_position}% through conversation ({relative_timing})\n")
                if start_turn != 'N/A' and end_turn != 'N/A':
                    file.write(f"📍 Conversation Turns: {start_turn}-{end_turn}\n")

                # Question codes with details
                if question_codes:
                    file.write(f"🏷️  OASIS Question Codes ({len(question_codes)}):\n")
                    for code in question_codes:
                        question_info = question_map.get(code, {})
                        question_text = question_info.get('question', 'Unknown question')
                        label = question_info.get('labelName', 'Unknown')
                        section = question_info.get('section', 'Unknown')
                        file.write(f"   • {code}: {label}\n")
                        file.write(f"     Section: {section}\n")
                        file.write(f"     Question: {question_text[:100]}{'...' if len(question_text) > 100 else ''}\n")
                else:
                    file.write(f"🏷️  OASIS Question Codes: None assigned\n")

                file.write("\n💬 CONTENT PREVIEW:\n")

                # Format content preview
                if 'CLINICIAN:' in doc_content or 'PATIENT:' in doc_content:
                    # Semantic chunks are already formatted
                    lines = doc_content.split('\n')[:5]  # Show first 5 lines
                    for line in lines:
                        line = line.strip()
                        if line:
                            if line.startswith('CLINICIAN:'):
                                file.write(f"🩺 {line}\n")
                            elif line.startswith('PATIENT:'):
                                file.write(f"👤 {line}\n")
                            else:
                                file.write(f"💬 {line}\n")
                    if len(doc_content.split('\n')) > 5:
                        file.write("   ... (content truncated)\n")
                else:
                    # Show raw content preview
                    preview = doc_content[:300] + "..." if len(doc_content) > 300 else doc_content
                    file.write(f"💬 {preview}\n")

                file.write("\n" + "=" * 80 + "\n\n")

        return analysis_filename

    except Exception as e:
        logger.error(f"Error creating chunk analysis report: {e}")
        return None


def fix_json_with_llm(problematic_input: str, error_details: str, openai_api_key: str) -> str:
    """
    Use LLM to fix JSON parsing errors by sending the problematic input and error details.

    Args:
        problematic_input: The input string that failed to parse as JSON
        error_details: Details about the JSON parsing error
        openai_api_key: OpenAI API key for the LLM call

    Returns:
        Fixed JSON string, or original input if fixing fails
    """
    try:
        llm = ChatOpenAI(
            model=RAGConfig.DEFAULT_LLM_MODEL,
            openai_api_key=openai_api_key
        )

        fix_prompt = f"""You are a JSON repair expert. The following text was supposed to be valid JSON but failed to parse with this error:

ERROR: {error_details}

PROBLEMATIC INPUT:
{problematic_input}

Please fix the JSON formatting issues and return ONLY the corrected JSON. Common issues to fix:
1. Missing or extra commas
2. Unescaped quotes in strings
3. Trailing commas
4. Missing brackets or braces
5. Invalid escape sequences
6. Malformed numbers or booleans

Return only the corrected JSON without any explanation or markdown formatting:"""

        response = llm.invoke(fix_prompt)
        fixed_json = response.content.strip()

        # Clean up any markdown formatting that might have been added
        if fixed_json.startswith("```json"):
            fixed_json = fixed_json.replace("```json", "").replace("```", "").strip()
        elif fixed_json.startswith("```"):
            fixed_json = fixed_json.replace("```", "").strip()

        # Test if the fixed JSON is valid
        try:
            json.loads(fixed_json)
            logger.info("LLM successfully fixed the JSON")
            return fixed_json
        except json.JSONDecodeError as test_error:
            logger.warning(f"LLM fix attempt still has JSON errors: {test_error}")
            return problematic_input

    except Exception as e:
        logger.error(f"Error during LLM JSON fixing: {e}")
        return problematic_input


def parse_and_validate_llm_response(response: str, question_answer_list: list, openai_api_key: str = None) -> bool:
    """
    Parse and validate LLM response JSON, adding valid items to question_answer_list.
    If JSON parsing fails, attempts to fix it using LLM.

    Args:
        response: Raw response string from LLM
        question_answer_list: List to append valid question-answer items to
        openai_api_key: OpenAI API key for JSON fixing (optional)

    Returns:
        bool: True if parsing and validation succeeded, False otherwise

    Raises:
        Exception: Re-raises any validation or parsing exceptions for testing
    """
    # Uncomment the line below to add a breakpoint for debugging
    # breakpoint()

    if not response:
        return False

    # Clean the response string
    string = response.replace("```", "").replace("json", "").strip()

    # Uncomment the line below to debug string cleaning
    # breakpoint()

    try:
        # Parse JSON
        json_data = json.loads(string)
        logger.info(f"type of json_data: {type(json_data)}")

        # Uncomment the line below to debug after JSON parsing
        # breakpoint()

        # Validate and append based on data type
        if isinstance(json_data, list):
            jsonschema.validate(instance=json_data, schema=response_schema)
            for item in json_data:
                question_answer_list.append(item)
        else:
            jsonschema.validate(instance=json_data, schema=single_response_schema)
            question_answer_list.append(json_data)

        return True

    except json.JSONDecodeError as json_error:
        # Specific handling for JSON decode errors
        logger.warning(f"JSON decode error: {json_error}")
        logger.info("Attempting to fix JSON using LLM...")

        if openai_api_key:
            # Try to fix the JSON using LLM
            fixed_json_string = fix_json_with_llm(string, str(json_error), openai_api_key)

            # Try parsing the fixed JSON
            try:
                json_data = json.loads(fixed_json_string)
                logger.info("Successfully parsed LLM-fixed JSON")

                # Validate and append the fixed data
                if isinstance(json_data, list):
                    jsonschema.validate(instance=json_data, schema=response_schema)
                    for item in json_data:
                        question_answer_list.append(item)
                else:
                    jsonschema.validate(instance=json_data, schema=single_response_schema)
                    question_answer_list.append(json_data)

                return True

            except (json.JSONDecodeError, jsonschema.ValidationError) as retry_error:
                logger.error(f"LLM-fixed JSON still has errors: {retry_error}")
                # Fall through to general exception handling
        else:
            logger.warning("No OpenAI API key provided for JSON fixing")

        # If JSON fixing failed or no API key, treat as general exception
        logger.error(f"JSON parsing failed and could not be fixed: {json_error}")
        raise json_error

    except Exception as ve:
        # Uncomment the line below to debug exceptions
        # breakpoint()
        logger.error(f"JSON does not match expected schema: {ve}")

        # send_slack_message(f"JSON does not match expected schema: {ve}")
        raise ve  # Re-raise for testing purposes


def pre_process_questions(questions:list):
    for question in questions:
        # remove the answer_context from each question if it exists
        if "answer_context" in question:
            question.pop("answer_context")

        # Check if the question has options and options is an array
        if "options" in question and isinstance(question["options"], list):
            # Add "Not Available" to options if it's not already in the list
            if "Not Available" not in question["options"]:
                question["options"].append("Not Available")

    return questions

# Removed complex tagging function - using simple chunking now


# Removed complex tagging and enhanced retriever functions - using simple approach now
def answer_questions(conversation_data: dict, questions: list, client_id: str, assessment_id: str,
                    embedding_model: str = "text-embedding-3-large", custom_name_suffix: str = ""):
    """
    Main function to answer questions using LangChain RAG

    Args:
        conversation_data: The conversation data
        questions: List of questions to answer
        client_id: Client identifier
        assessment_id: Assessment identifier
        embedding_model: Embedding model to use (default: OpenAI's text-embedding-ada-002)
    """
    # Initialize settings first - specify the correct .env file path
    import os
    current_dir = os.path.dirname(os.path.abspath(__file__))
    env_file_path = os.path.join(current_dir, "..", "..", ".env")

    settings, _ = create_app_settings("ai_core", AiCoreConfig, env_file=env_file_path)
    openai.api_key = settings.openai_key

    if not settings.openai_key:
        print("ERROR: OpenAI key not found in settings!")
        return None

    questions = pre_process_questions(questions)

    rag_system = None

    try:
        # Initialize LangChain RAG system
        rag_system = LangChainRAG(
            embedding_model=embedding_model,
            openai_api_key=settings.openai_key
        )

        # Create vector store from conversation data using simple chunking
        rag_system.create_vectorstore_from_conversation(conversation_data, client_id)

        # Setup QA chain
        rag_system.setup_qa_chain(
            llm_model=settings.openai_model,
            # temperature=0.0
        )

        # Process questions in OASIS groups with subgrouping for large groups
        assessment_splitted_list = split_assessment_list_by_oasis_groups(questions, RAGConfig.MAX_SUBGROUP_SIZE)
        user_content_list = prepare_user_content(assessment_splitted_list, user_content_default, rag_system)

        question_answer_list = []
        retrieval_data_list = []

        # Process each chunk
        with ThreadPoolExecutor() as executor:
            futures = []
            for user_content in user_content_list:
                future = executor.submit(get_llm_response_langchain, rag_system, user_content)
                futures.append(future)

            for future in futures:
                try:
                    result = future.result()
                    response = result["answer"]
                    retrieval_info = result["retrieval_info"]

                    # Store retrieval information
                    retrieval_data_list.append({
                        "timestamp": time.time(),
                        "embedding_model": embedding_model,
                        "client_id": client_id,
                        "assessment_id": assessment_id,
                        "retrieval_info": retrieval_info
                    })

                    # Parse and validate the LLM response
                    try:
                        parse_and_validate_llm_response(response, question_answer_list, settings.openai_key)
                    except Exception:
                        # Exception is already logged in the function, just continue processing
                        pass


                except Exception as e:
                    logger.error(f"Error processing future: {e}")

        # Success message (commented out for clean output)
        # slack_message = (f"Whole process completed successfully with Question-Answer pair (LangChain).\n\n"
        # f"Date : *{time.ctime(time.time())}*\n"
        # f"Client ID : *{client_id}*\n"
        # f"Assessment ID : *{assessment_id}*\n"
        # f"Embedding Model : *{embedding_model}*")
        # send_slack_message(slack_message)

        # Save retrieval data to separate file (commented for deployment)
        suffix_part = f"_{custom_name_suffix}" if custom_name_suffix else ""
        # Use high precision timestamp + unique ID to avoid collisions in multi-threading
        timestamp = f"{time.time():.6f}".replace(".", "_")
        unique_id = str(uuid.uuid4())[:8]  # Short unique identifier
        thread_id = threading.current_thread().ident
        retrieval_filename = f"retrieval_chunks{suffix_part}_{timestamp}_{unique_id}_{thread_id}.json"

        retrieval_summary = {
            "embedding_model": embedding_model,
            "client_id": client_id,
            "assessment_id": assessment_id,
            "timestamp": time.time(),
            "total_queries": len(retrieval_data_list),
            "retrieval_data": retrieval_data_list
        }

        # File generation commented out for deployment
        # DEBUGGING
        # with open(retrieval_filename, "w", encoding="utf-8") as file:
        #     json.dump(retrieval_summary, file, indent=2)

        # Output file generation can be enabled if needed
        # readable_filename = save_human_readable_retrieval(
        #     retrieval_data_list, embedding_model, client_id, assessment_id
        # )

        # chunk_analysis_filename = save_chunk_analysis_report(
        #     rag_system.vectorstore, embedding_model, client_id, assessment_id, questions
        # )

        final_data_dict = {
            "ClientID": client_id,
            "Responses": question_answer_list
        }

        return final_data_dict

    except Exception as e:
        error_message = (
            f"Something went wrong while generating question-answer file (LangChain).\n\n"
            f"Date : *{time.ctime(time.time())}*\n"
            f"Client ID : *{client_id}*\n"
            f"Assessment ID : *{assessment_id}*\n"
            f"Embedding Model : *{embedding_model}*\n"
            f"Error : *{str(e)}*\n"
            f"Line no. : *{e.__traceback__.tb_lineno}*"
        )
        logger.error(error_message)
        # send_slack_message(error_message)
        return None

    finally:
        # Cleanup
        if rag_system:
            rag_system.cleanup()


def main(custom_name_suffix=""):
    """
    Main function for testing - same structure as original but with LangChain

    Args:
        custom_name_suffix: Custom string to append to output filename
    """
    # Configure logging to only show errors
    logging.basicConfig(
        level=logging.ERROR,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler()  # This outputs to console
        ]
    )

    try:
        # Initialize settings first - specify the correct .env file path
        from config import create_app_settings
        import os

        # Get the correct path to .env file (go up from src directory)
        current_dir = os.path.dirname(os.path.abspath(__file__))
        env_file_path = os.path.join(current_dir, "..", "..", ".env")

        settings, _ = create_app_settings("ai_core", AiCoreConfig, env_file=env_file_path)

        # Check if OpenAI key is loaded
        if not settings.openai_key:
            print("ERROR: OpenAI key not found in settings!")
            return
        with open(os.path.join("tests", "integration", "test_data","M18","weightrange.json"), "r", encoding="utf-8") as file:
            conversation_data = json.load(file)

        with open(os.path.join("tests", "integration", "test_data","M18","questionsAll.json"), "r", encoding="utf-8") as file:
            questions = json.load(file)

        # 🔄 EASY MODEL SWITCHING - Uncomment the models you want to test
        embedding_models = [
            # === OpenAI Models ===
            # "text-embedding-ada-002",        # OpenAI default - fast and cheap
            # "text-embedding-3-small",      # OpenAI newer - better performance
            "text-embedding-3-large",      # OpenAI best - highest quality

            # === Medical/Clinical Models ===
            # "michiyasunaga/BioLinkBERT-large",  # BioLinkBERT - biomedical specialist
            # "emilyalsentzer/Bio_ClinicalBERT",  # BioClinical BERT - medical specialist
            # "microsoft/BiomedNLP-PubMedBERT-base-uncased-abstract-fulltext",  # PubMed BERT

            # === General Purpose Models ===
            # "sentence-transformers/all-MiniLM-L6-v2",     # Fast general purpose
            # "sentence-transformers/all-mpnet-base-v2",    # High quality general purpose
        ]

        for embedding_model in embedding_models:
            _ = answer_questions(
                conversation_data,
                questions,
                client_id="kate",
                assessment_id="assessment-1",
                embedding_model=embedding_model,
                custom_name_suffix=custom_name_suffix
            )

            # Save results with embedding model in filename
            # Use high precision timestamp + unique ID to avoid collisions in multi-threading
            # timestamp = f"{time.time():.6f}".replace(".", "_")
            # unique_id = str(uuid.uuid4())[:8]
            # thread_id = threading.current_thread().ident
            # suffix_part = f"_{custom_name_suffix}" if custom_name_suffix else ""
            # filename = f"answers_langchain{suffix_part}_{timestamp}_{unique_id}_{thread_id}.json"
            # with open(os.path.join(filename), "w", encoding="utf-8") as file:
            #     json.dump(answers, file, indent=2)

    except Exception as e:
        logger.error(f"Error in main: {e}", exc_info=True)


def run_single_evaluation(run_number, total_runs, custom_suffix):
    """
    Run a single evaluation - wrapper function for threading
    """
    try:
        main(custom_name_suffix=custom_suffix)
        return f"Run {run_number} completed successfully"
    except Exception as e:
        logger.error(f"Error in run {run_number}: {e}")
        return f"Run {run_number} failed: {e}"


if __name__ == "__main__":
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Run question answering evaluation with custom parameters')
    parser.add_argument('--suffix', '-s', type=str, default='',
                       help='Custom string to append to output filename (e.g., "experiment1")')
    parser.add_argument('--runs', '-r', type=int, default=1,
                       help='Number of times to run the evaluation (default: 5)')
    parser.add_argument('--threads', '-t', type=int, default=1,
                       help='Number of parallel threads to use (default: 3)')

    args = parser.parse_args()

    # Use ThreadPoolExecutor for parallel execution
    with ThreadPoolExecutor(max_workers=args.threads) as executor:
        # Submit all tasks
        futures = []
        for i in range(args.runs):
            future = executor.submit(run_single_evaluation, i+1, args.runs, args.suffix)
            futures.append(future)

        # Wait for all tasks to complete and collect results
        results = []
        for future in futures:
            try:
                result = future.result()
                results.append(result)
            except Exception as e:
                results.append(f"Thread execution failed: {e}")
