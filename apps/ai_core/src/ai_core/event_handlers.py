# unmanaged
from fastapi import Request
from database.interface import DatabaseAdapter
from models.database_s3_config import DatabaseS3Config
from models.database_sqlite_config import DatabaseSqliteConfig
from database.factory import create_database_client
from queues.factory import create_queue_client
from queues.interface import QueueClient
from config import get_settings
from ai_core.models.config import Config as AiCoreConfig
from models.queue_sqs_config import QueueSqsConfig
from models.queue_local_config import QueueLocalConfig
from models.database_filesystem_config import DatabaseFilesystemConfig
from models.database_mongodb_config import DatabaseMongodbConfig
from messages.factory import create_message_client
from models.messages_local_config import MessagesLocalConfig
from models.messages_slack_config import MessagesSlackConfig
from monitor.factory import create_monitor, get_monitor
from models.monitor_local_config import MonitorLocalConfig
from messages.factory import get_message_client
import logging


def on_startup():
    logging.info("Application is starting up.")
    settings: AiCoreConfig = get_settings()

    if settings.database_type == "filesystem":
        create_database_client(DatabaseFilesystemConfig(name="default"))
        create_database_client(DatabaseSqliteConfig(name="sqlite"))
    else:
        # default original db (s3)
        create_database_client(
            DatabaseS3Config(
                name="default",
                bucket_name=settings.bucket_name,
                aws_access_key_id=settings.aws_access_key_id,
                aws_secret_access_key=settings.aws_secret_access_key,
            )
        )
        # sqlite for annotations, comments, ratings
        create_database_client(DatabaseSqliteConfig(name="sqlite"))

    create_database_client(DatabaseMongodbConfig(name="scribble_admin", 
                                                 mongodb_replica_uri=settings.mongodb_replica_uri, 
                                                 mongodb_database_name="scribble_admin", 
                                                 mongodb_max_pool_size=settings.mongodb_max_pool_size, 
                                                 mongodb_min_pool_size=settings.mongodb_min_pool_size))

    # input and output queues
    if settings.queue_type == "local":
        create_queue_client(
            QueueLocalConfig(
                name=settings.queue_input_name, queue_url=settings.queue_input_name
            )
        )
        create_queue_client(
            QueueLocalConfig(
                name=settings.queue_output_name, queue_url=settings.queue_output_name
            )
        )
    else:
        create_queue_client(
            QueueSqsConfig(
                name=settings.queue_input_name,
                aws_access_key_id=settings.aws_access_key_id,
                aws_secret_access_key=settings.aws_secret_access_key,
                queue_url=settings.queue_input_name,
            )
        )
        create_queue_client(
            QueueSqsConfig(
                name=settings.queue_output_name,
                aws_access_key_id=settings.aws_access_key_id,
                aws_secret_access_key=settings.aws_secret_access_key,
                queue_url=settings.queue_output_name,
            )
        )

    if settings.slack_enabled:
        create_message_client(
            MessagesSlackConfig(
                slack_token=settings.slack_bot_token,
                channel=settings.slack_channel,
                prefix=settings.slack_prefix,
            )
        )
    else:
        create_message_client(MessagesLocalConfig())

    # message_client = get_message_client()

    # Resource monitor
    if settings.resource_monitor_enabled:
        create_monitor(
            MonitorLocalConfig(
                check_disk_space_used_threshold_percent=settings.check_disk_space_used_threshold_percent,
                check_memory_used_threshold_percent=settings.check_memory_used_threshold_percent,
                check_load_average_threshold_percent=settings.check_load_average_threshold_percent,
            )
        )
        monitor = get_monitor()
        if monitor:
            monitor.start()

    logging.info("Resources initialized.")


def on_shutdown():
    logging.info("Application is shutting down.")


def on_new_registration(item, db: DatabaseAdapter, q: QueueClient, request: Request):
    print("A new user has registered.")
