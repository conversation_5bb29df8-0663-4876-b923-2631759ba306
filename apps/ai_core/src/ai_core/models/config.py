from pydantic import BaseModel, Field
from typing import List, Any, Optional, Literal, Dict
import time
import json
import yaml

from models.config_base import ConfigBase

# .env file for configuration

class Config(ConfigBase):
    id: Optional[str] = Field("default", example="default")
    app_server_enabled: Optional[bool] = Field(True, description="Enable app server", example=True)
    resource_monitor_enabled: Optional[bool] = Field(True, description="Enable resource monitor", example=True)
    pipeline_enabled: Optional[bool] = Field(True, description="Enable pipeline", example=True)
    port: Optional[int] = Field(8001, example="8001")
    log_level: Literal["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"] = Field("INFO", description="Log Level", example="INFO")
    ssl_enabled: Optional[bool] = Field(False, description="Enable SSL", example=True)
    auth_enabled: Optional[bool] = Field(False, description="Enable authentication", example=True)
    database_type: Literal["s3", "none", "tinydb", "dynamodb", "filesystem"] = Field("s3", description="Database type", example="dynamodb")
    tenant_reports_database_type: Literal["mongodb"] = Field("mongodb", description="Database type for tenant reports (MongoDB only)", example="mongodb")
    database_dir: Optional[str] = Field(None, description="Database directory - default is programmatically data", example="data")
    bucket_name: Optional[str] = Field("scribble2-data", description="S3 bucket name", example="scribble2-data")
    debug: Optional[bool] = Field(True, description="Enable debug mode", example=True)
    queue_only: Optional[bool] = Field(True, description="Run only queue listener - else run FastAPI and queue listener", example=True)
    queue_type: Literal["local", "sqs"] = Field("sqs", description="Queue type", example="local")
    queue_input_name: Optional[str] = Field(None, description="SQS Queue URL", example="https://sqs.us-west-2.amazonaws.com/123456789012/input-queue")
    queue_output_name: Optional[str] = Field(None, description="SQS Queue URL", example="https://sqs.us-west-2.amazonaws.com/123456789012/outpu-queue")
    deepgram_api_key: Optional[str] = Field(None, example="xxxxxxxxxxxxxxxxxxxxxxxxxxxx")
    deepgram_model: Optional[str] = Field("nova-2", example="nova-2")
    openai_key: Optional[str] = Field(None, description="OpenAI access key", example="your-access-key")
    openai_model: Optional[str] = Field("gpt-5-mini", description="OpenAI model", example="gpt-5-mini")
    aws_access_key_id: Optional[str] = Field(None, description="AWS Access Key ID", example="your-access-key")
    aws_secret_access_key: Optional[str] = Field(None, description="AWS Secret Access Key", example="your-secret-key")
    slack_enabled: Optional[bool] = Field(True, description="Enable Slack notifications", example=True)
    slack_channel: Optional[str] = Field("scribble-alerts", description="Slack channel", example="scribble-alerts")
    slack_bot_token: Optional[str] = Field(None, description="Slack bot token", example="xxxxxxxx")
    slack_prefix: Optional[str] = Field(None, description="Prefix to message", example="AI Core - DEV:")
    ai_temperature: Optional[float] = Field(0.0, example=0.5)
    test_scribble_backend_base_url: Optional[str] = Field(None, description="Base URL for testing", example="https://api-dev.goscribble.ai")
    test_user_email: Optional[str] = Field(None, description="Test user email", example="<EMAIL>")
    test_password: Optional[str] = Field(None, description="Test user password", example="test_password")
    test_tenant_id: Optional[str] = Field(None, description="Test tenant ID", example="test_tenant_id")
    tenant_reports_database_type: Literal["mongodb"] = Field("mongodb", description="Database type for tenant reports (MongoDB only)", example="mongodb")
    # MongoDB Configuration (Read-only)
    mongodb_replica_uri: Optional[str] = Field(None, description="MongoDB replica connection string for read operations", example="mongodb://localhost:27017")
    mongodb_database_name: Optional[str] = Field("scribble_admin", description="MongoDB database name", example="scribble_admin")
    mongodb_max_pool_size: Optional[int] = Field(10, description="MongoDB maximum connection pool size", example=10)
    mongodb_min_pool_size: Optional[int] = Field(1, description="MongoDB minimum connection pool size", example=1)
    check_disk_space_used_threshold_percent: Optional[int] = Field(80, description="Threshold percentage for disk space alerts", example=90)
    check_memory_used_threshold_percent: Optional[int] = Field(90, description="Threshold percentage for memory available alerts", example=90)
    check_load_average_threshold_percent: Optional[float] = Field(95, description="Threshold for load average usage percent alerts", example=95)

    @classmethod
    def from_yaml(cls, yaml_str: str):
        """Load Config from a YAML string."""
        data = yaml.safe_load(yaml_str)
        return cls(**data)

    @classmethod
    def from_yaml_file(cls, file_path: str):
        """Load Config from a YAML file."""
        with open(file_path, "r") as file:
            return cls.from_yaml(file.read())

    @classmethod
    def from_json(cls, json_str: str):
        """Load Config from a JSON string."""
        data = json.loads(json_str)
        return cls(**data)

    @classmethod
    def from_json_file(cls, file_path: str):
        """Load Config from a JSON file."""
        with open(file_path, "r") as file:
            return cls.from_json(file.read())