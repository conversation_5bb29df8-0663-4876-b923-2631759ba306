from pydantic import BaseModel, Field, field_validator
from typing import List, Dict, Optional
from datetime import datetime

class TenantInfo(BaseModel):
    _id: str
    tenantId: str  # Alias for _id for frontend convenience
    tenantName: str
    uniqueName: str
    databaseName: str
    createdAt: str
    updatedAt: str
    
    @field_validator('createdAt', 'updatedAt', mode='before')
    @classmethod
    def convert_datetime(cls, v):
        if isinstance(v, datetime):
            return v.isoformat()
        return v

class SimpleTenantInfo(BaseModel):
    tenantId: str
    tenantName: str

class VisitInfo(BaseModel):
    _id: str
    episodeId: str
    clinicianId: str
    clientId: str
    visitNo: str
    visitDate: str
    visitStartTime: str
    visitType: str
    status: Optional[str] = None  # Make status optional since it's missing in some records
    createdAt: str
    updatedAt: str
    
    @field_validator('createdAt', 'updatedAt', 'visitDate', 'visitStartTime', 'episodeId', 'clinicianId', 'clientId', mode='before')
    @classmethod
    def convert_fields(cls, v):
        if isinstance(v, datetime):
            return v.isoformat()
        elif hasattr(v, '__str__'):  # Handle ObjectId and other objects
            return str(v)
        return v

class TenantReportResponse(BaseModel):
    tenant: TenantInfo
    visits: List[VisitInfo]
    total_visits: int
    visits_by_status: Dict[str, int]
    visits_by_type: Dict[str, int]
