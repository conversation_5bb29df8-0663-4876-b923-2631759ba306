from pydantic import BaseModel, Field
from typing import List, Any, Optional, Literal, Dict
import time
import json
import yaml

class Registration(BaseModel):
    id: Optional[str] = Field(None, example="123e4567-e89b-12d3-a456-426614174000")
    email: Optional[str] = Field(None, example="<EMAIL>")
    password_hash: Optional[str] = Field(None, example="bdf39900sdfk3")
    password: Optional[str] = Field(None, example="bdf39900sdfk3")
    state: Literal["registered", "verified", "mfa", "active", "deactivated"] = Field("registered", description="Registration for a user", example="registered")
    verification_token: Optional[str] = Field(None, example="123e4567-e89b-12d3-a456-426614174000")
    created: Optional[int] = Field(None, example=1683123456789)

    @classmethod
    def from_yaml(cls, yaml_str: str):
        """Load Registration from a YAML string."""
        data = yaml.safe_load(yaml_str)
        return cls(**data)

    @classmethod
    def from_yaml_file(cls, file_path: str):
        """Load Registration from a YAML file."""
        with open(file_path, "r") as file:
            return cls.from_yaml(file.read())

    @classmethod
    def from_json(cls, json_str: str):
        """Load Registration from a JSON string."""
        data = json.loads(json_str)
        return cls(**data)

    @classmethod
    def from_json_file(cls, file_path: str):
        """Load Registration from a JSON file."""
        with open(file_path, "r") as file:
            return cls.from_json(file.read())