import logging
import json
import asyncio
import argparse
import os
from config import create_app_settings
from ai_core.models.config import Config as AiCoreConfig
from ai_core.pipeline import Pipeline
from ai_core.server import AppServer
from ai_core.event_handlers import on_startup, on_shutdown

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

VERSION_FILE = "version.json"

def load_version():
    """Load the version from version.json safely (synchronous, simple)."""
    try:
        if os.path.exists(VERSION_FILE):
            with open(VERSION_FILE, "r") as f:
                return json.load(f)
    except (json.JSONDecodeError, IOError) as e:
        logger.warning(f"Error loading version.json: {e}")
    return {"version": "0.0.0"}


def parse_args():
    """Parse CLI args synchronously."""
    parser = argparse.ArgumentParser(description="AI Core Main")
    parser.add_argument("--env_file", default=".env", help="Path to the .env file (default: .env)")
    args = parser.parse_args()
    logger.info(f"VIRTUAL_ENV: {os.environ.get('VIRTUAL_ENV', '(not set)')}")
    logger.info(f"PYTHONPATH: {os.environ.get('PYTHONPATH', '(not set)')}")
    return args

# load version at import time for simple, synchronous flow
version = load_version()
logger.info(f"Loaded version: {version}")

async def run():
    args = parse_args()
    settings, _ = create_app_settings("ai_core", AiCoreConfig, env_file=args.env_file)
    settings: AiCoreConfig = settings

    on_startup()

    components = []

    num_pipelines = settings.num_pipelines if hasattr(settings, "num_pipelines") else 1

    if num_pipelines >= 1:
        components.extend([Pipeline(settings) for _ in range(num_pipelines)])

    if settings.app_server_enabled:
        components.append(AppServer(settings))

    try:
        await asyncio.gather(*(comp.run() for comp in components))
    finally:
        for comp in components:
            stop = getattr(comp, "stop", None)
            if stop is None:
                continue
            try:
                result = stop()
                if asyncio.iscoroutine(result):
                    await result
            except Exception:
                logger.exception("Error stopping component %s", comp.__class__.__name__)
        on_shutdown()

if __name__ == "__main__":
    asyncio.run(run())