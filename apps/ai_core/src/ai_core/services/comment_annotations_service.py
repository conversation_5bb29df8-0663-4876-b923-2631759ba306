# unmanaged
# FIXME - Request probably should not be here
# instead individual query parameters should be defined in the app.yml
# then put into a dictionary parameter to the service methods
from fastapi import Request
import logging
from typing import List
import uuid
from config import get_settings
from queues.interface import QueueClient
from database.interface import DatabaseAdapter
from ai_core.models.comment_annotations import CommentAnnotations
from ai_core.service_response import info_message, error_message, warning_message
from database.factory import get_database_client
from pathlib import Path


logger = logging.getLogger(__name__)

# write - Create an item
def create_comment_annotations(item: CommentAnnotations, user: dict, request: Request):
    logger.info("===============create_comment_annotations called==============")

    # Get SQLite database instance from factory (will default to data/sqlite_db.sqlite3)
    sqlite_db = get_database_client("sqlite")

    item_id = item.id if hasattr(item, "id") and item.id else str(uuid.uuid4())
    logger.info(f"Using item_id: {item_id}")
    new_item = item.model_dump()
    new_item["id"] = item_id  # Store UUID in the database

    logger.info(item)

    # Use SQLite database instead of passed-in db
    sqlite_db.insert_item("comment_annotations", item_id, new_item)
    logger.info(f"CommentAnnotations created: {new_item}")
    # if q:
    #     q.send_message(new_item)
    #     logger.info(f"Message sent to queue: TranscriptionRating created: {new_item}")
    #     logger.info(f"Queue message count: {q.get_message_count()}")
    return new_item

# read - get all items
def get_all_comment_annotations(user: dict, request: Request):
    logger.info("===============get_all_comment_annotations called==============")
    sqlite_db = get_database_client("sqlite")
    all_items = sqlite_db.get_all_items("comment_annotations")
    return all_items

# read - get an item
def get_comment_annotations(id: str, user: dict, request: Request):
    logger.info("===============get_comment_annotations called==============")
    logger.info(f"Received request to retrieve comment_annotations with id: {id}")
    sqlite_db = get_database_client("sqlite")

    item = sqlite_db.get_item("comment_annotations", id)
    return item

# read - get_path an item
def get_path_comment_annotations(path: str, user: dict, request: Request):
    logger.info("===============get_comment_annotations called==============")
    sqlite_db = get_database_client("sqlite")

    item = sqlite_db.get_item("comment_annotations", path)
    return item


# write - update an item (without modifying ID)
def update_comment_annotations(id: str, new_item: CommentAnnotations, user: dict, request: Request):
    logger.info("===============update_comment_annotations called==============")
    sqlite_db = get_database_client("sqlite")

    sqlite_db.update_item("comment_annotations", id, new_item.model_dump())
    return sqlite_db.get_item("comment_annotations", id)

# write - delete an item
def delete_comment_annotations(id: str, user: dict, request: Request):
    logger.info("===============delete_comment_annotations called==============")
    logger.info(f"Received request to delete comment_annotations with id {id}")
    sqlite_db = get_database_client("sqlite")

    item = sqlite_db.get_item("comment_annotations", id)
    if not item:
        logger.warning(f"CommentAnnotations with id {id} not found")
        return None
    sqlite_db.delete_item("comment_annotations", id)
    return item