import logging
from typing import List, Dict, Any, Optional, Union
from datetime import datetime, timedelta
import json
from ai_core.models.config import Config as AiCoreConfig
from models.database_mongodb_config import DatabaseMongodbConfig
from database.interface import DatabaseAdapter
from database.factory import get_database, get_database_client, create_database_client
from ai_core.service_response import ServiceResponseMessage, info_message, error_message
from config import get_settings

logger = logging.getLogger(__name__)

tenant_dbs = {}

def _parse_date_range(start_date: Optional[str], end_date: Optional[str]) -> tuple:
    """Parse and validate date range parameters."""
    from datetime import timezone
    
    start_dt = None
    end_dt = None
    
    if start_date:
        try:
            start_dt = datetime.strptime(start_date, "%Y-%m-%d")
            # Convert to UTC datetime
            start_dt = start_dt.replace(tzinfo=timezone.utc)
        except ValueError:
            raise ValueError("Invalid start_date format. Use YYYY-MM-DD")
    
    if end_date:
        try:
            end_dt = datetime.strptime(end_date, "%Y-%m-%d")
            # Set to end of day and convert to UTC
            end_dt = end_dt.replace(hour=23, minute=59, second=59, tzinfo=timezone.utc)
        except ValueError:
            raise ValueError("Invalid end_date format. Use YYYY-MM-DD")
    
    return start_dt, end_dt

def _build_visit_date_filter(start_dt: Optional[datetime], end_dt: Optional[datetime]) -> Dict[str, Any]:
    """Build MongoDB date filter for visit dates."""
    date_filter = {}
    
    if start_dt or end_dt:
        if start_dt and end_dt:
            # Both start and end date
            start_str = start_dt.strftime("%Y-%m-%d")
            end_str = end_dt.strftime("%Y-%m-%d")
            date_filter = {
                "visitDate": {
                    "$gte": f"{start_str}T00:00:00",
                    "$lte": f"{end_str}T23:59:59"
                }
            }
        elif start_dt:
            # Only start date - use string comparison
            start_str = start_dt.strftime("%Y-%m-%d")
            date_filter = {
                "visitDate": {
                    "$gte": f"{start_str}T00:00:00"
                }
            }
        elif end_dt:
            # Only end date - use string comparison
            end_str = end_dt.strftime("%Y-%m-%d")
            date_filter = {
                "visitDate": {
                    "$lte": f"{end_str}T23:59:59"
                }
            }
    
    logger.info(f"Built date filter: {date_filter}")
    return date_filter

def _get_tenant_database(tenant_id: str, admin_db: DatabaseAdapter) -> DatabaseAdapter:
    """Get tenant database connection based on tenant ID."""
    try:
        # Get tenant info from scribble_admin database
        tenant_info = admin_db.get_item("tenants", tenant_id)
        if not tenant_info:
            raise ValueError(f"Tenant not found: {tenant_id}")
        
        database_name = tenant_info.get("databaseName")
        if not database_name:
            raise ValueError(f"No database name found for tenant: {tenant_id}")
        
        logger.info(f"Connecting to tenant database: {database_name} for tenant: {tenant_id}")
        
        # Create a new database connection for the tenant's database
        # We'll use the same MongoDB connection but switch to the tenant's database
                
        # Create tenant-specific database connection using the factory function
        # We need to create a config provider that returns the tenant-specific database name
        settings: AiCoreConfig = get_settings()
        if tenant_id in tenant_dbs and tenant_dbs[tenant_id] is not None:
            return tenant_dbs[tenant_id]
        
        logger.info(f"Cached tenant_db for {tenant_id} is None!")
            # Proceed to create a new connection
        create_database_client(DatabaseMongodbConfig(name=tenant_id, 
                                                 mongodb_replica_uri=settings.mongodb_replica_uri, 
                                                 mongodb_database_name=database_name, 
                                                 mongodb_max_pool_size=settings.mongodb_max_pool_size, 
                                                 mongodb_min_pool_size=settings.mongodb_min_pool_size))
        tenant_dbs[tenant_id] = get_database_client(tenant_id)
        
        logger.info(f"Successfully connected to tenant database: {database_name}")
        return tenant_dbs[tenant_id]
        
    except Exception as e:
        logger.error(f"Error getting tenant database for {tenant_id}: {e}")
        raise

def get_tenants(
    db: DatabaseAdapter,
    user: dict,
    request: Any
) -> Union[List[Dict[str, Any]], ServiceResponseMessage]:
    """Get list of all available tenants from scribble_admin database."""
    try:
        # Get all tenants from the tenants collection
        tenants = db.get_all_items("tenants")
        
        # Convert ObjectIds to strings and format the response
        formatted_tenants = []
        for tenant in tenants:
            tenant_id = str(tenant.get("_id", ""))
            formatted_tenant = {
                "tenantId": tenant_id,
                "tenantName": tenant.get("tenantName", "")
            }
            formatted_tenants.append(formatted_tenant)
        
        return formatted_tenants
        
    except Exception as e:
        logger.error(f"Error in get_tenants: {e}")
        return error_message(f"Failed to get tenants: {str(e)}", 500)

def get_tenant_visits(
    tenant_id: str,
    db: DatabaseAdapter,
    user: dict,
    request: Any,
    status: Optional[str] = None,
    visit_type: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    limit: int = 50,
    offset: int = 0
) -> Union[Dict[str, Any], ServiceResponseMessage]:
    """Get visits for a specific tenant."""
    try:
        # Get tenant database connection
        tenant_db = _get_tenant_database(tenant_id, db)
        
        # Build filter criteria
        filter_criteria = {}
        
        logger.info(f"Initial filter criteria: {filter_criteria}")
        

        

        
        # Add status filter if provided
        if status:
            filter_criteria["status"] = status
        
        # Add visit type filter if provided
        if visit_type:
            filter_criteria["visitType"] = visit_type
        
        # Get visits from tenant database
        visits = tenant_db.query_items("visits", filter_criteria)
        logger.info(f"Retrieved {len(visits)} visits from database")
        
        # Apply date filtering in Python if needed
        if start_date or end_date:
            start_dt, end_dt = _parse_date_range(start_date, end_date)
            
            # If only start_date is provided
            if start_date and not end_date:
                current_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
                if start_dt.date() < current_date.date():
                    # If start date is before current date, show from start date to current date
                    end_dt = current_date
                    logger.info(f"Start date {start_dt.date()} is before current date, showing from {start_dt.date()} to {end_dt.date()}")
                else:
                    # If start date is after current date, show 7 days from start date
                    end_dt = start_dt + timedelta(days=7)
                    logger.info(f"Start date {start_dt.date()} is after current date, showing 7 days from {start_dt.date()} to {end_dt.date()}")
            elif end_date and not start_date:
                # If only end date is provided
                current_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
                if end_dt.date() < current_date.date():
                    # If end date is in the past, don't show any records
                    logger.info(f"End date {end_dt.date()} is in the past, showing no records")
                    visits = []
                    # Continue with empty visits list to get proper response format
                else:
                    # If end date is in the future, show from current date to end date
                    start_dt = current_date
                    logger.info(f"End date {end_dt.date()} is in the future, showing from current date {start_dt.date()} to {end_dt.date()}")
            
            filtered_visits = []
            
            for visit in visits:
                visit_date = visit.get('visitDate', '')
                if not visit_date:
                    continue
                
                # Parse the visit date
                try:
                    # Handle different date formats
                    if isinstance(visit_date, datetime):
                        visit_dt = visit_date
                    elif isinstance(visit_date, str):
                        visit_date_clean = visit_date.strip()
                        
                        # Try different date formats
                        visit_dt = None
                        date_formats = [
                            "%Y-%m-%dT%H:%M:%S",  # ISO format
                            "%m/%d/%Y",            # MM/DD/YYYY format
                            "%Y-%m-%d",            # YYYY-MM-DD format
                            "%d/%m/%Y",            # DD/MM/YYYY format
                        ]
                        
                        for date_format in date_formats:
                            try:
                                visit_dt = datetime.strptime(visit_date_clean, date_format)
                                break
                            except ValueError:
                                continue
                        
                        if visit_dt is None:
                            logger.warning(f"Could not parse visit date '{visit_date}' for visit {visit.get('visitNo', visit.get('no', 'unknown'))}")
                            continue
                    else:
                        logger.warning(f"Unknown visit date type: {type(visit_date)} for visit {visit.get('visitNo', visit.get('no', 'unknown'))}")
                        continue
                    
                    # Check if visit date is within range
                    include_visit = True
                    if start_dt and visit_dt.date() < start_dt.date():
                        include_visit = False
                    if end_dt and visit_dt.date() > end_dt.date():
                        include_visit = False
                    
                    if include_visit:
                        filtered_visits.append(visit)
                        
                except ValueError as e:
                    logger.warning(f"Could not parse visit date '{visit_date}' for visit {visit.get('no', 'unknown')}: {e}")
                    continue
            
            visits = filtered_visits
            logger.info(f"Date filtering applied: {len(filtered_visits)} visits found for date range {start_date} to {end_date}")
        else:
            logger.info(f"No date filtering applied, returning all {len(visits)} visits")
        

        

        

        

        

        

        

        

        

        

        

        

        

        
        # Normalize visit data to handle different field naming conventions
        def normalize_visit(visit):
            normalized = visit.copy()
            # Handle visit number field (visitNo vs no vs visitId)
            if 'visitNo' not in normalized and 'no' in normalized:
                normalized['visitNo'] = normalized['no']
            elif 'no' not in normalized and 'visitNo' in normalized:
                normalized['no'] = normalized['visitNo']
            elif 'visitNo' not in normalized and 'no' not in normalized and 'visitId' in normalized:
                normalized['visitNo'] = normalized['visitId']
                normalized['no'] = normalized['visitId']
            # Handle start time field (visitStartTime vs startTime)
            if 'visitStartTime' not in normalized and 'startTime' in normalized:
                normalized['visitStartTime'] = normalized['startTime']
            elif 'startTime' not in normalized and 'visitStartTime' in normalized:
                normalized['startTime'] = normalized['visitStartTime']
            # Handle visit type field (visitType vs type)
            if 'visitType' not in normalized and 'type' in normalized:
                normalized['visitType'] = normalized['type']
            elif 'type' not in normalized and 'visitType' in normalized:
                normalized['type'] = normalized['visitType']
            return normalized
        
        # Normalize all visits
        visits = [normalize_visit(visit) for visit in visits]
        
        # Apply pagination
        total_count = len(visits)
        paginated_visits = visits[offset:offset + limit]
        
        # Sort by visit date (newest first)
        paginated_visits.sort(key=lambda x: x.get("visitDate", ""), reverse=True)
        
        # Calculate statistics
        visits_by_status = {}
        visits_by_type = {}
        
        for visit in visits:
            # Count by status
            visit_status = visit.get("status", "Unknown")
            visits_by_status[visit_status] = visits_by_status.get(visit_status, 0) + 1
            
            # Count by type
            visit_type_val = visit.get("type", "Unknown")
            visits_by_type[visit_type_val] = visits_by_type.get(visit_type_val, 0) + 1
        
        # Get tenant info
        tenant_info = db.get_item("tenants", tenant_id)
        if not tenant_info:
            return error_message(f"Tenant not found: {tenant_id}", 404)
        
        # Format response
        tenant_id = str(tenant_info.get("_id", ""))
        return {
            "tenant": {
                "_id": tenant_id,
                "tenantId": tenant_id,  # Alias for frontend convenience
                "tenantName": tenant_info.get("tenantName", ""),
                "uniqueName": tenant_info.get("uniqueName", ""),
                "databaseName": tenant_info.get("databaseName", ""),
                "createdAt": tenant_info.get("createdAt", ""),
                "updatedAt": tenant_info.get("updatedAt", "")
            },
            "visits": paginated_visits,
            "total_visits": total_count,
            "visits_by_status": visits_by_status,
            "visits_by_type": visits_by_type
        }
        
    except Exception as e:
        logger.error(f"Error in get_tenant_visits: {e}")
        return error_message(f"Failed to get tenant visits: {str(e)}", 500)

def get_visit_details(
    tenant_id: str,
    visit_id: str,
    db: DatabaseAdapter,
    user: dict,
    request: Any
) -> Union[Dict[str, Any], ServiceResponseMessage]:
    """Get detailed information about a specific visit."""
    try:
        # Get tenant database connection
        tenant_db = _get_tenant_database(tenant_id, db)
        
        # Try to get the visit by _id first, then by 'no' or 'visitNo' field
        visit = tenant_db.get_item("visits", visit_id)
        if not visit:
            # If not found by _id, try to find by 'no' field
            visits = tenant_db.query_items("visits", {"no": visit_id})
            if visits:
                visit = visits[0]
            else:
                # Try to find by 'visitNo' field
                visits = tenant_db.query_items("visits", {"visitNo": visit_id})
                if visits:
                    visit = visits[0]
                else:
                    return error_message("Visit not found", 404)
        
        # Convert ObjectId fields to strings to avoid serialization issues
        def convert_objectids(obj):
            if isinstance(obj, dict):
                converted = {}
                for key, value in obj.items():
                    if hasattr(value, '__str__') and str(type(value)) == "<class 'bson.objectid.ObjectId'>":
                        converted[key] = str(value)
                    else:
                        converted[key] = convert_objectids(value)
                return converted
            elif isinstance(obj, list):
                return [convert_objectids(item) for item in obj]
            else:
                return obj
        
        # Convert the visit object
        visit = convert_objectids(visit)
        
        # Normalize visit data to handle different field naming conventions
        def normalize_visit(visit):
            normalized = visit.copy()
            
            # Handle visit number field (visitNo vs no)
            if 'visitNo' not in normalized and 'no' in normalized:
                normalized['visitNo'] = normalized['no']
            elif 'no' not in normalized and 'visitNo' in normalized:
                normalized['no'] = normalized['visitNo']
            
            # Handle start time field (visitStartTime vs startTime)
            if 'visitStartTime' not in normalized and 'startTime' in normalized:
                normalized['visitStartTime'] = normalized['startTime']
            elif 'startTime' not in normalized and 'visitStartTime' in normalized:
                normalized['startTime'] = normalized['visitStartTime']
            
            # Handle visit type field (visitType vs type)
            if 'visitType' not in normalized and 'type' in normalized:
                normalized['visitType'] = normalized['type']
            elif 'type' not in normalized and 'visitType' in normalized:
                normalized['type'] = normalized['visitType']
            
            return normalized
        
        # Normalize the visit
        visit = normalize_visit(visit)
        
        # Get related data if available
        details = {
            "visit": visit,
            "related_data": {}
        }
        
        # Try to get client information
        try:
            client_id = visit.get("clientId")
            if client_id:
                client = tenant_db.get_item("clients", client_id)
                if client:
                    details["related_data"]["client"] = convert_objectids(client)
                else:
                    details["related_data"]["client"] = None
        except:
            details["related_data"]["client"] = None
        
        # Try to get episode information
        try:
            episode_id = visit.get("episodeId")
            if episode_id:
                episode = tenant_db.get_item("episodes", episode_id)
                if episode:
                    details["related_data"]["episode"] = convert_objectids(episode)
                else:
                    details["related_data"]["episode"] = None
        except:
            details["related_data"]["episode"] = None
        
        # Try to get clinician information
        try:
            clinician_id = visit.get("clinicianId")
            if clinician_id:
                clinician = tenant_db.get_item("clinician_infos", clinician_id)
                if clinician:
                    details["related_data"]["clinician"] = convert_objectids(clinician)
                else:
                    details["related_data"]["clinician"] = None
        except:
            details["related_data"]["clinician"] = None
        
        return details
        
    except Exception as e:
        logger.error(f"Error in get_visit_details: {e}")
        return error_message(f"Failed to get visit details: {str(e)}", 500)

def get_tenant_analytics(
    tenant_id: str,
    db: DatabaseAdapter,
    user: dict,
    request: Any,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None
) -> Union[Dict[str, Any], ServiceResponseMessage]:
    """Get analytics data for a specific tenant."""
    try:
        # Get tenant database connection
        tenant_db = _get_tenant_database(tenant_id, db)
        
        # Get all visits first
        visits = tenant_db.query_items("visits", {})
        logger.info(f"Retrieved {len(visits)} visits for analytics")
        
        # Apply date filtering in Python if needed
        if start_date or end_date:
            start_dt, end_dt = _parse_date_range(start_date, end_date)
            
            # If only start_date is provided
            if start_date and not end_date:
                current_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
                if start_dt.date() < current_date.date():
                    # If start date is before current date, show from start date to current date
                    end_dt = current_date
                    logger.info(f"Start date {start_dt.date()} is before current date, showing from {start_dt.date()} to {end_dt.date()}")
                else:
                    # If start date is after current date, show 7 days from start date
                    end_dt = start_dt + timedelta(days=7)
                    logger.info(f"Start date {start_dt.date()} is after current date, showing 7 days from {start_dt.date()} to {end_dt.date()}")
            elif end_date and not start_date:
                # If only end date is provided
                current_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
                if end_dt.date() < current_date.date():
                    # If end date is in the past, don't show any records
                    logger.info(f"End date {end_dt.date()} is in the past, showing no records")
                    visits = []
                    return {
                        "total_visits": 0,
                        "visits_by_status": {},
                        "visits_by_type": {},
                        "time_series_data": []
                    }
                else:
                    # If end date is in the future, show from current date to end date
                    start_dt = current_date
                    logger.info(f"End date {end_dt.date()} is in the future, showing from current date {start_dt.date()} to {end_dt.date()}")
        else:
            # If no dates selected, show last 7 days
            end_dt = datetime.now()
            start_dt = end_dt - timedelta(days=7)
            logger.info(f"No date range specified, showing last 7 days: {start_dt.date()} to {end_dt.date()}")
        
        filtered_visits = []
        
        for visit in visits:
            visit_date = visit.get('visitDate', '')
            if not visit_date:
                continue
            
            # Parse the visit date
            try:
                # Handle different date formats
                if isinstance(visit_date, datetime):
                    visit_dt = visit_date
                elif isinstance(visit_date, str):
                    # Remove timezone if present and parse
                    visit_date_clean = visit_date.split('+')[0]  # Remove timezone
                    visit_dt = datetime.strptime(visit_date_clean, "%Y-%m-%dT%H:%M:%S")
                else:
                    logger.warning(f"Unknown visit date type: {type(visit_date)} for visit {visit.get('no', 'unknown')}")
                    continue
                
                # Check if visit date is within range
                include_visit = True
                if start_dt and visit_dt.date() < start_dt.date():
                    include_visit = False
                if end_dt and visit_dt.date() > end_dt.date():
                    include_visit = False
                
                if include_visit:
                    filtered_visits.append(visit)
                    
            except ValueError as e:
                logger.warning(f"Could not parse visit date '{visit_date}' for visit {visit.get('no', 'unknown')}: {e}")
                continue
        
        visits = filtered_visits
        logger.info(f"Date filtering applied: {len(filtered_visits)} visits found for analytics date range {start_dt.date()} to {end_dt.date()}")
        
        # Calculate analytics
        total_visits = len(visits)
        
        # Visits by status
        visits_by_status = {}
        for visit in visits:
            status = visit.get("status", "Unknown")
            visits_by_status[status] = visits_by_status.get(status, 0) + 1
        
        # Visits by type
        visits_by_type = {}
        for visit in visits:
            visit_type = visit.get("type", "Unknown")
            visits_by_type[visit_type] = visits_by_type.get(visit_type, 0) + 1
        
        # Time series data based on the actual date range
        time_series_data = []
        
        # Use the filtered date range for time series
        start_date_for_series = start_dt
        end_date_for_series = end_dt
        
        current_date = start_date_for_series
        while current_date <= end_date_for_series:
            day_visits = []
            current_date_str = current_date.strftime("%Y-%m-%d")
            
            for visit in visits:
                visit_date = visit.get('visitDate', '')
                if not visit_date:
                    continue
                
                try:
                    # Handle different date formats
                    if isinstance(visit_date, datetime):
                        visit_dt = visit_date
                    elif isinstance(visit_date, str):
                        # Remove timezone if present and parse
                        visit_date_clean = visit_date.split('+')[0]  # Remove timezone
                        visit_dt = datetime.strptime(visit_date_clean, "%Y-%m-%dT%H:%M:%S")
                    else:
                        continue
                    
                    # Check if visit is on this day
                    if visit_dt.date() == current_date.date():
                        day_visits.append(visit)
                        
                except ValueError:
                    continue
            
            time_series_data.append({
                "date": current_date_str,
                "count": len(day_visits)
            })
            
            current_date += timedelta(days=1)
        
        # Top clients by visit count
        client_stats = {}
        for visit in visits:
            client_id = visit.get("clientId")
            if client_id:
                if client_id not in client_stats:
                    client_stats[client_id] = {"count": 0, "client_name": "Unknown"}
                client_stats[client_id]["count"] += 1
        
        # Get client names for top clients
        for client_id in client_stats:
            try:
                client_info = tenant_db.get_item("clients", client_id)
                if client_info:
                    # Try to get client name from various possible fields
                    client_name = (
                        client_info.get("firstName", "") + " " + client_info.get("lastName", "")
                    ).strip()
                    if not client_name:
                        client_name = client_info.get("name", "Unknown")
                    if not client_name:
                        client_name = client_info.get("clientName", "Unknown")
                    client_stats[client_id]["client_name"] = client_name
                else:
                    client_stats[client_id]["client_name"] = "Unknown"
            except Exception as e:
                logger.warning(f"Could not get client info for {client_id}: {e}")
                client_stats[client_id]["client_name"] = "Unknown"
        
        top_clients = sorted(
            [{"client_id": cid, "client_name": stats["client_name"], "count": stats["count"]} 
             for cid, stats in client_stats.items()],
            key=lambda x: x["count"],
            reverse=True
        )[:10]
        
        # Convert ObjectIds to strings for JSON serialization
        def convert_objectids(obj):
            if isinstance(obj, dict):
                return {k: convert_objectids(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_objectids(item) for item in obj]
            elif hasattr(obj, '__str__') and str(type(obj)) == "<class 'bson.objectid.ObjectId'>":
                return str(obj)
            elif isinstance(obj, datetime):
                return obj.isoformat()
            else:
                return obj
        
        # Convert all ObjectId fields in the response
        response_data = {
            "total_visits": total_visits,
            "visits_by_status": visits_by_status,
            "visits_by_type": visits_by_type,
            "time_series_data": time_series_data,
            "top_clients": top_clients
        }
        
        return convert_objectids(response_data)
        
    except Exception as e:
        logger.error(f"Error in get_tenant_analytics: {e}")
        return error_message(f"Failed to get tenant analytics: {str(e)}", 500)

def export_tenant_data(
    tenant_id: str,
    data_type: str,
    format: str,
    db: DatabaseAdapter,
    user: dict,
    request: Any,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None
) -> Union[Dict[str, Any], ServiceResponseMessage]:
    """Export tenant data in various formats."""
    try:
        # Get tenant database connection
        tenant_db = _get_tenant_database(tenant_id, db)
        
        # Build date filter if provided
        filter_criteria = {}
        if start_date or end_date:
            start_dt, end_dt = _parse_date_range(start_date, end_date)
            date_filter = _build_visit_date_filter(start_dt, end_dt)
            if date_filter:
                filter_criteria.update(date_filter)
                logger.info(f"Export date filter applied: {date_filter}")
        
        if data_type == "visits":
            # Get all visits first, then apply Python-based date filtering
            all_visits = tenant_db.query_items("visits", {})
            logger.info(f"Retrieved {len(all_visits)} visits for export")
            
            # Get tenant info for tenant name
            tenant_info = db.get_item("tenants", tenant_id)
            tenant_name = tenant_info.get("tenantName", "Unknown Tenant") if tenant_info else "Unknown Tenant"
            
            # Enrich visits with additional information
            enriched_visits = []
            for visit in all_visits:
                enriched_visit = visit.copy()
                
                # Add tenant name
                enriched_visit["tenantName"] = tenant_name
                
                # Get client information
                client_id = visit.get("clientId")
                if client_id:
                    # Try different possible collection names for clients
                    client_info = None
                    for collection_name in ["clients", "client_infos", "client_info", "client_information"]:
                        try:
                            client_info = tenant_db.get_item(collection_name, client_id)
                            if client_info:
                                logger.info(f"Found client info in collection {collection_name}")
                                break
                        except Exception as e:
                            logger.debug(f"Failed to get client from {collection_name}: {e}")
                            continue
                    
                    if client_info:
                        # Try different possible field names for client name
                        client_name = (
                            client_info.get("firstName", "") + " " + client_info.get("lastName", "")
                        ).strip()
                        if not client_name:
                            client_name = client_info.get("name", "")
                        if not client_name:
                            client_name = client_info.get("clientName", "")
                        if not client_name:
                            client_name = client_info.get("fullName", "")
                        enriched_visit["clientName"] = client_name or "Unknown Client"
                    else:
                        enriched_visit["clientName"] = "Unknown Client"
                else:
                    enriched_visit["clientName"] = "Unknown Client"
                
                # Get clinician information
                clinician_id = visit.get("clinicianId")
                if clinician_id:
                    # Try different possible collection names for clinicians
                    clinician_info = None
                    for collection_name in ["clinician_infos", "clinicians", "clinician_info", "clinician_information"]:
                        try:
                            clinician_info = tenant_db.get_item(collection_name, clinician_id)
                            if clinician_info:
                                logger.info(f"Found clinician info in collection {collection_name}")
                                break
                        except Exception as e:
                            logger.debug(f"Failed to get clinician from {collection_name}: {e}")
                            continue
                    
                    if clinician_info:
                        # Try different possible field names for clinician name
                        clinician_name = (
                            clinician_info.get("firstName", "") + " " + clinician_info.get("lastName", "")
                        ).strip()
                        if not clinician_name:
                            clinician_name = clinician_info.get("name", "")
                        if not clinician_name:
                            clinician_name = clinician_info.get("clinicianName", "")
                        if not clinician_name:
                            clinician_name = clinician_info.get("fullName", "")
                        enriched_visit["clinicianName"] = clinician_name or "Unknown Clinician"
                    else:
                        enriched_visit["clinicianName"] = "Unknown Clinician"
                else:
                    enriched_visit["clinicianName"] = "Unknown Clinician"
                
                enriched_visits.append(enriched_visit)
            
            all_visits = enriched_visits
            
            # Apply date filtering in Python if needed
            if start_date or end_date:
                start_dt, end_dt = _parse_date_range(start_date, end_date)
                
                # If only start_date is provided
                if start_date and not end_date:
                    current_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
                    if start_dt.date() < current_date.date():
                        # If start date is before current date, show from start date to current date
                        end_dt = current_date
                        logger.info(f"Start date {start_dt.date()} is before current date, showing from {start_dt.date()} to {end_dt.date()}")
                    else:
                        # If start date is after current date, show 7 days from start date
                        end_dt = start_dt + timedelta(days=7)
                        logger.info(f"Start date {start_dt.date()} is after current date, showing 7 days from {start_dt.date()} to {end_dt.date()}")
                elif end_date and not start_date:
                    # If only end date is provided
                    current_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
                    if end_dt.date() < current_date.date():
                        # If end date is in the past, don't show any records
                        logger.info(f"End date {end_dt.date()} is in the past, showing no records")
                        visits = []
                        return {
                            "total_visits": 0,
                            "visits_by_status": {},
                            "visits_by_type": {},
                            "time_series_data": []
                        }
                    else:
                        # If end date is in the future, show from current date to end date
                        start_dt = current_date
                        logger.info(f"End date {end_dt.date()} is in the future, showing from current date {start_dt.date()} to {end_dt.date()}")
                
                filtered_visits = []
                
                for visit in all_visits:
                    visit_date = visit.get('visitDate', '')
                    if not visit_date:
                        continue
                    
                    # Parse the visit date
                    try:
                        # Handle different date formats
                        if isinstance(visit_date, datetime):
                            visit_dt = visit_date
                        elif isinstance(visit_date, str):
                            visit_date_clean = visit_date.strip()
                            
                            # Try different date formats
                            visit_dt = None
                            date_formats = [
                                "%Y-%m-%dT%H:%M:%S",  # ISO format
                                "%m/%d/%Y",            # MM/DD/YYYY format
                                "%Y-%m-%d",            # YYYY-MM-DD format
                                "%d/%m/%Y",            # DD/MM/YYYY format
                            ]
                            
                            for date_format in date_formats:
                                try:
                                    visit_dt = datetime.strptime(visit_date_clean, date_format)
                                    break
                                except ValueError:
                                    continue
                            
                            if visit_dt is None:
                                logger.warning(f"Could not parse visit date '{visit_date}' for visit {visit.get('visitNo', visit.get('no', 'unknown'))}")
                                continue
                        else:
                            logger.warning(f"Unknown visit date type: {type(visit_date)} for visit {visit.get('visitNo', visit.get('no', 'unknown'))}")
                            continue
                        
                        # Check if visit date is within range
                        include_visit = True
                        if start_dt and visit_dt.date() < start_dt.date():
                            include_visit = False
                        if end_dt and visit_dt.date() > end_dt.date():
                            include_visit = False
                        
                        if include_visit:
                            filtered_visits.append(visit)
                            
                    except ValueError as e:
                        logger.warning(f"Could not parse visit date '{visit_date}' for visit {visit.get('no', 'unknown')}: {e}")
                        continue
                
                data = filtered_visits
                logger.info(f"Date filtering applied: {len(filtered_visits)} visits found for export date range {start_dt.date()} to {end_dt.date()}")
            else:
                data = all_visits
                
        elif data_type == "analytics":
            # For analytics, call the analytics function and return its data
            analytics_result = get_tenant_analytics(tenant_id, db, user, request, start_date, end_date)
            if isinstance(analytics_result, ServiceResponseMessage):
                return analytics_result
            data = analytics_result
            
        elif data_type == "clients":
            data = tenant_db.query_items("clients", filter_criteria)
        elif data_type == "episodes":
            data = tenant_db.query_items("episodes", filter_criteria)
        else:
            return error_message(f"Unknown data type: {data_type}", 400)
        
        # Convert ObjectId fields to strings for JSON serialization
        def convert_objectids(obj):
            if isinstance(obj, dict):
                return {k: convert_objectids(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_objectids(item) for item in obj]
            elif hasattr(obj, '__str__') and str(type(obj)) == "<class 'bson.objectid.ObjectId'>":
                return str(obj)
            elif isinstance(obj, datetime):
                return obj.isoformat()
            else:
                return obj
        
        # Convert all ObjectId fields in the data
        converted_data = convert_objectids(data)
        
        if format == "json":
            return {
                "format": "json",
                "data_type": data_type,
                "tenant_id": tenant_id,
                "data": converted_data,
                "exported_at": datetime.now().isoformat()
            }
        elif format == "csv":
            # For CSV, we'll return the data as JSON and let the frontend handle CSV conversion
            return {
                "format": "csv",
                "data_type": data_type,
                "tenant_id": tenant_id,
                "data": converted_data,
                "exported_at": datetime.now().isoformat()
            }
        else:
            return error_message(f"Unsupported format: {format}", 400)
        
    except Exception as e:
        logger.error(f"Error in export_tenant_data: {e}")
        return error_message(f"Failed to export tenant data: {str(e)}", 500)
