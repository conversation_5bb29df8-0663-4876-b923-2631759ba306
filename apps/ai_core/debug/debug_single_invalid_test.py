"""
Focused debug script for testing a single invalid response scenario with maximum detail.

This script allows you to test one specific invalid response scenario at a time
with step-by-step debugging and detailed inspection of the function's behavior.
"""

import json
import sys
import os
import traceback
import logging
from unittest.mock import patch, MagicMock

# Configure detailed logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Add the src directory to the path so we can import the module
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, '..', 'src')
sys.path.insert(0, src_dir)

try:
    from ai_core.answer_questions_langchain_knowledgebase_subgrouping import (
        parse_and_validate_llm_response,
        response_schema,
        single_response_schema
    )
    print("✅ Successfully imported required modules")
except ImportError as e:
    print(f"❌ Import error: {e}")
    # Try alternative import
    try:
        sys.path.insert(0, os.path.join(current_dir, '..'))
        from src.ai_core.answer_questions_langchain_knowledgebase_subgrouping import (
            parse_and_validate_llm_response,
            response_schema,
            single_response_schema
        )
        print("✅ Successfully imported required modules (alternative path)")
    except ImportError:
        # Last resort - direct file import
        import importlib.util
        module_path = os.path.join(src_dir, 'ai_core', 'answer_questions_langchain_knowledgebase_subgrouping.py')
        if os.path.exists(module_path):
            spec = importlib.util.spec_from_file_location("answer_questions_module", module_path)
            answer_questions_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(answer_questions_module)

            parse_and_validate_llm_response = answer_questions_module.parse_and_validate_llm_response
            response_schema = answer_questions_module.response_schema
            single_response_schema = answer_questions_module.single_response_schema
            print("✅ Successfully imported required modules (direct import)")
        else:
            print(f"❌ Module file not found at: {module_path}")
            sys.exit(1)


def debug_step_by_step_invalid_json():
    """Debug invalid JSON step by step with maximum detail."""
    print("\n" + "="*80)
    print("🔍 STEP-BY-STEP DEBUG: Invalid JSON Response")
    print("="*80)
    
    # Test setup
    question_answer_list = []
    invalid_json = "```json\n{invalid json content\n```"
    
    print("📋 STEP 1: Initial Setup")
    print(f"   - question_answer_list: {question_answer_list}")
    print(f"   - Raw response: {repr(invalid_json)}")
    
    # Step 2: String cleaning simulation
    print("\n📋 STEP 2: String Cleaning Simulation")
    cleaned_string = invalid_json.replace("```", "").replace("json", "").strip()
    print(f"   - After removing '```': {repr(invalid_json.replace('```', ''))}")
    print(f"   - After removing 'json': {repr(invalid_json.replace('```', '').replace('json', ''))}")
    print(f"   - After strip(): {repr(cleaned_string)}")
    
    # Step 3: JSON parsing attempt
    print("\n📋 STEP 3: JSON Parsing Attempt")
    try:
        json_data = json.loads(cleaned_string)
        print(f"   - JSON parsing succeeded: {json_data}")
    except Exception as e:
        print(f"   - JSON parsing failed: {type(e).__name__}: {e}")
        print(f"   - Error position: {getattr(e, 'pos', 'N/A')}")
        print(f"   - Error line: {getattr(e, 'lineno', 'N/A')}")
        print(f"   - Error column: {getattr(e, 'colno', 'N/A')}")
    
    # Step 4: Full function call
    print("\n📋 STEP 4: Full Function Call")
    mock_logger = MagicMock()
    
    try:
        print("   - Calling parse_and_validate_llm_response...")
        
        with patch('ai_core.answer_questions_langchain_knowledgebase_subgrouping.logger', mock_logger):
            result = parse_and_validate_llm_response(invalid_json, question_answer_list)
            
        print(f"   - Function returned: {result}")
        print(f"   - Final list length: {len(question_answer_list)}")
        
    except Exception as e:
        print(f"   - Exception caught: {type(e).__name__}: {e}")
        print(f"   - Logger calls made: {mock_logger.method_calls}")
        
    print("\n📋 STEP 5: Final State")
    print(f"   - question_answer_list: {question_answer_list}")
    print(f"   - List length: {len(question_answer_list)}")


def debug_step_by_step_missing_field():
    """Debug missing required field step by step with maximum detail."""
    print("\n" + "="*80)
    print("🔍 STEP-BY-STEP DEBUG: Missing Required Field")
    print("="*80)
    
    # Test setup
    question_answer_list = []
    invalid_response = {
        "question_text": "Primary Diagnosis",
        "question_type": "text",
        "answer_context": "Patient has diabetes",
        "answer_reason": "Based on medical history", 
        "answer_text": "Diabetes mellitus",
        "confidence_score": 0.95
    }
    
    response_string = f"```json\n{json.dumps(invalid_response, indent=2)}\n```"
    
    print("📋 STEP 1: Initial Setup")
    print(f"   - question_answer_list: {question_answer_list}")
    print(f"   - Missing field: 'question_code'")
    print(f"   - Response object keys: {list(invalid_response.keys())}")
    
    # Step 2: Check schema requirements
    print("\n📋 STEP 2: Schema Requirements Check")
    print(f"   - Required fields in single_response_schema: {single_response_schema['required']}")
    print(f"   - Fields present in response: {list(invalid_response.keys())}")
    missing_fields = set(single_response_schema['required']) - set(invalid_response.keys())
    print(f"   - Missing fields: {missing_fields}")
    
    # Step 3: String cleaning simulation
    print("\n📋 STEP 3: String Cleaning")
    cleaned_string = response_string.replace("```", "").replace("json", "").strip()
    print(f"   - Cleaned string length: {len(cleaned_string)}")
    print(f"   - First 100 chars: {cleaned_string[:100]}...")
    
    # Step 4: JSON parsing
    print("\n📋 STEP 4: JSON Parsing")
    try:
        json_data = json.loads(cleaned_string)
        print(f"   - JSON parsing succeeded")
        print(f"   - Data type: {type(json_data)}")
        print(f"   - Data keys: {list(json_data.keys()) if isinstance(json_data, dict) else 'N/A'}")
    except Exception as e:
        print(f"   - JSON parsing failed: {e}")
        return
    
    # Step 5: Schema validation attempt
    print("\n📋 STEP 5: Schema Validation")
    try:
        import jsonschema
        if isinstance(json_data, list):
            print("   - Validating as list against response_schema...")
            jsonschema.validate(instance=json_data, schema=response_schema)
        else:
            print("   - Validating as single object against single_response_schema...")
            jsonschema.validate(instance=json_data, schema=single_response_schema)
        print("   - Schema validation succeeded")
    except Exception as e:
        print(f"   - Schema validation failed: {type(e).__name__}")
        print(f"   - Error message: {str(e)[:200]}...")
        print(f"   - Failed field: {getattr(e, 'path', 'N/A')}")
        print(f"   - Schema path: {getattr(e, 'schema_path', 'N/A')}")
    
    # Step 6: Full function call
    print("\n📋 STEP 6: Full Function Call")
    mock_logger = MagicMock()
    
    try:
        with patch('ai_core.answer_questions_langchain_knowledgebase_subgrouping.logger', mock_logger):
            result = parse_and_validate_llm_response(response_string, question_answer_list)
            
        print(f"   - Function returned: {result}")
        
    except Exception as e:
        print(f"   - Exception caught: {type(e).__name__}: {str(e)[:100]}...")
        print(f"   - Logger error called: {mock_logger.error.called}")
        
    print("\n📋 STEP 7: Final State")
    print(f"   - question_answer_list length: {len(question_answer_list)}")
    print(f"   - Items added: {question_answer_list}")


def debug_step_by_step_type_mismatch():
    """Debug type mismatch step by step."""
    print("\n" + "="*80)
    print("🔍 STEP-BY-STEP DEBUG: Type Mismatch (String instead of Object)")
    print("="*80)
    
    # Test setup
    question_answer_list = []
    wrong_data = "just a string"
    response_string = f"```json\n{json.dumps(wrong_data)}\n```"
    
    print("📋 STEP 1: Initial Setup")
    print(f"   - Expected: object or array")
    print(f"   - Actual: {type(wrong_data).__name__}")
    print(f"   - Value: {repr(wrong_data)}")
    
    # Step 2: JSON parsing
    print("\n📋 STEP 2: JSON Parsing")
    cleaned_string = response_string.replace("```", "").replace("json", "").strip()
    json_data = json.loads(cleaned_string)
    print(f"   - Parsed data type: {type(json_data)}")
    print(f"   - Is list?: {isinstance(json_data, list)}")
    print(f"   - Is dict?: {isinstance(json_data, dict)}")
    
    # Step 3: Schema selection logic
    print("\n📋 STEP 3: Schema Selection Logic")
    if isinstance(json_data, list):
        print("   - Will use response_schema (for arrays)")
        schema_to_use = "response_schema"
    else:
        print("   - Will use single_response_schema (for objects)")
        schema_to_use = "single_response_schema"
    
    # Step 4: Schema validation
    print("\n📋 STEP 4: Schema Validation")
    try:
        import jsonschema
        if isinstance(json_data, list):
            jsonschema.validate(instance=json_data, schema=response_schema)
        else:
            jsonschema.validate(instance=json_data, schema=single_response_schema)
        print("   - Validation succeeded")
    except Exception as e:
        print(f"   - Validation failed: {type(e).__name__}")
        print(f"   - Expected type: object")
        print(f"   - Actual type: {type(json_data).__name__}")
    
    # Step 5: Full function call
    print("\n📋 STEP 5: Full Function Call")
    mock_logger = MagicMock()
    
    try:
        with patch('ai_core.answer_questions_langchain_knowledgebase_subgrouping.logger', mock_logger):
            result = parse_and_validate_llm_response(response_string, question_answer_list)
        print(f"   - Function returned: {result}")
    except Exception as e:
        print(f"   - Exception: {type(e).__name__}: {str(e)[:100]}...")


def main():
    """Run focused debug tests."""
    print("🐛 FOCUSED DEBUG MODE: Invalid Response Test Cases")
    print("=" * 80)
    
    # Choose which test to run (uncomment the one you want)
    debug_step_by_step_invalid_json()
    debug_step_by_step_missing_field()
    debug_step_by_step_type_mismatch()
    
    print("\n" + "=" * 80)
    print("🏁 Focused debug testing completed!")


if __name__ == "__main__":
    main()
