import pytest
import os
import sys
from unittest.mock import Mock, patch
from datetime import datetime, timedelta

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from database.mongodb_database import MongoDBDatabase
from database.factory import get_database

class TestMongoDBIntegration:
    """Test MongoDB read-only integration."""
    
    def test_mongodb_database_initialization(self):
        """Test that MongoDB database can be initialized with replica URI."""
        # Mock the MongoClient to avoid actual database connection
        with patch('database.mongodb_database.MongoClient') as mock_client:
            mock_db = Mock()
            mock_client.return_value.__getitem__.return_value = mock_db
            
            # Test initialization
            db = MongoDBDatabase(
                replica_uri="mongodb://test-replica:27017/scribble",
                database_name="scribble",
                max_pool_size=5,
                min_pool_size=1
            )
            
            # Verify client was created with correct parameters
            mock_client.assert_called_once()
            call_args = mock_client.call_args
            assert call_args[0][0] == "mongodb://test-replica:27017/scribble"
            assert call_args[1]['maxPoolSize'] == 5
            assert call_args[1]['minPoolSize'] == 1
    
    def test_read_only_operations(self):
        """Test that read operations work correctly."""
        with patch('database.mongodb_database.MongoClient') as mock_client:
            mock_db = Mock()
            mock_collection = Mock()
            mock_db.__getitem__.return_value = mock_collection
            mock_client.return_value.__getitem__.return_value = mock_db
            
            db = MongoDBDatabase(
                replica_uri="mongodb://test-replica:27017/scribble",
                database_name="scribble"
            )
            
            # Mock collection.find() to return test data
            mock_collection.find.return_value = [
                {'_id': 'test1', 'name': 'Test 1'},
                {'_id': 'test2', 'name': 'Test 2'}
            ]
            
            # Test get_all_items
            result = db.get_all_items("test_collection")
            assert len(result) == 2
            assert result[0]['_id'] == 'test1'
            assert result[1]['_id'] == 'test2'
    
    def test_write_operations_disabled(self):
        """Test that write operations raise NotImplementedError."""
        with patch('database.mongodb_database.MongoClient') as mock_client:
            mock_db = Mock()
            mock_client.return_value.__getitem__.return_value = mock_db
            
            db = MongoDBDatabase(
                replica_uri="mongodb://test-replica:27017/scribble",
                database_name="scribble"
            )
            
            # Test that write operations are disabled
            with pytest.raises(NotImplementedError, match="Write operations are not supported"):
                db.upsert_item("test", "key", {"data": "test"})
            
            with pytest.raises(NotImplementedError, match="Write operations are not supported"):
                db.insert_item("test", "key", {"data": "test"})
            
            with pytest.raises(NotImplementedError, match="Write operations are not supported"):
                db.update_item("test", "key", {"data": "test"})
            
            with pytest.raises(NotImplementedError, match="Write operations are not supported"):
                db.delete_item("test", "key")
    
    def test_date_filter_building(self):
        """Test date filter building for MongoDB queries."""
        from ai_core.services.reports_service import _build_date_filter
        
        # Test with start date only
        start_dt = datetime(2024, 1, 1)
        filter_result = _build_date_filter(start_dt, None)
        assert "started" in filter_result
        assert "$gte" in filter_result["started"]
        assert filter_result["started"]["$gte"] == int(start_dt.timestamp())
        
        # Test with end date only
        end_dt = datetime(2024, 1, 31)
        filter_result = _build_date_filter(None, end_dt)
        assert "started" in filter_result
        assert "$lte" in filter_result["started"]
        assert filter_result["started"]["$lte"] == int(end_dt.timestamp())
        
        # Test with both dates
        filter_result = _build_date_filter(start_dt, end_dt)
        assert "started" in filter_result
        assert "$gte" in filter_result["started"]
        assert "$lte" in filter_result["started"]
        assert filter_result["started"]["$gte"] == int(start_dt.timestamp())
        assert filter_result["started"]["$lte"] == int(end_dt.timestamp())
        
        # Test with no dates
        filter_result = _build_date_filter(None, None)
        assert filter_result == {}
    
    def test_date_parsing(self):
        """Test date parsing functionality."""
        from ai_core.services.reports_service import _parse_date_range
        
        # Test valid dates
        start_dt, end_dt = _parse_date_range("2024-01-01", "2024-01-31")
        assert start_dt == datetime(2024, 1, 1)
        assert end_dt == datetime(2024, 1, 31, 23, 59, 59)
        
        # Test with None values
        start_dt, end_dt = _parse_date_range(None, None)
        assert start_dt is None
        assert end_dt is None
        
        # Test invalid date format
        with pytest.raises(ValueError, match="Invalid start_date format"):
            _parse_date_range("invalid-date", None)
        
        with pytest.raises(ValueError, match="Invalid end_date format"):
            _parse_date_range(None, "invalid-date")
    
    def test_factory_mongodb_support(self):
        """Test that the factory supports MongoDB configuration."""
        config = {
            "database_type": "mongodb",
            "mongodb_replica_uri": "mongodb://test-replica:27017/scribble",
            "mongodb_database_name": "scribble",
            "mongodb_max_pool_size": "10",
            "mongodb_min_pool_size": "1"
        }
        
        config_provider = lambda: config
        
        with patch('database.mongodb_database.MongoClient') as mock_client:
            mock_db = Mock()
            mock_client.return_value.__getitem__.return_value = mock_db
            
            # Test that factory can create MongoDB database
            db = get_database(config_provider)
            assert isinstance(db, MongoDBDatabase)
            
            # Verify correct parameters were passed
            mock_client.assert_called_once()
            call_args = mock_client.call_args
            assert call_args[0][0] == "mongodb://test-replica:27017/scribble"
            assert call_args[1]['maxPoolSize'] == 10
            assert call_args[1]['minPoolSize'] == 1

if __name__ == "__main__":
    pytest.main([__file__])
