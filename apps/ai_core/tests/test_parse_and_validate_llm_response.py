"""
Test module for parse_and_validate_llm_response function.

This module demonstrates how to test the extracted function with mock responses
and various exception scenarios.
"""

import pytest
import json
import jsonschema
from unittest.mock import patch, MagicMock
import sys
import os

# Add the src directory to the path so we can import the module
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, '..', 'src')
sys.path.insert(0, src_dir)

try:
    from ai_core.src.ai_core.answer_questions_langchain_knowledgebase_subgrouping import (
        parse_and_validate_llm_response,
        response_schema,
        single_response_schema
    )
except ImportError as e:
    # Alternative import path for different environments
    sys.path.insert(0, os.path.join(current_dir, '..'))
    try:
        from src.ai_core.answer_questions_langchain_knowledgebase_subgrouping import (
            parse_and_validate_llm_response,
            response_schema,
            single_response_schema
        )
    except ImportError:
        # Last resort - direct file import
        import importlib.util
        module_path = os.path.join(src_dir, 'ai_core', 'answer_questions_langchain_knowledgebase_subgrouping.py')
        spec = importlib.util.spec_from_file_location("answer_questions_module", module_path)
        answer_questions_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(answer_questions_module)

        parse_and_validate_llm_response = answer_questions_module.parse_and_validate_llm_response
        response_schema = answer_questions_module.response_schema
        single_response_schema = answer_questions_module.single_response_schema


class TestParseAndValidateLLMResponse:
    """Test cases for parse_and_validate_llm_response function."""
    
    def setup_method(self):
        """Set up test fixtures before each test method."""
        self.question_answer_list = []
        
    def test_empty_response(self):
        """Test handling of empty response."""
        result = parse_and_validate_llm_response("", self.question_answer_list)
        assert result is False
        assert len(self.question_answer_list) == 0
        
    def test_none_response(self):
        """Test handling of None response."""
        result = parse_and_validate_llm_response(None, self.question_answer_list)
        assert result is False
        assert len(self.question_answer_list) == 0
        
    def test_valid_single_response(self):
        """Test parsing valid single response object."""
        mock_response = {
            "question_code": "M1021",
            "question_text": "Primary Diagnosis",
            "question_type": "text",
            "answer_context": "Patient has diabetes",
            "answer_reason": "Based on medical history",
            "answer_text": "Diabetes mellitus",
            "confidence_score": 0.95
        }
        
        response_string = f"```json\n{json.dumps(mock_response)}\n```"
        
        with patch('ai_core.answer_questions_langchain_knowledgebase_subgrouping.logger'):
            result = parse_and_validate_llm_response(response_string, self.question_answer_list)
            
        assert result is True
        assert len(self.question_answer_list) == 1
        assert self.question_answer_list[0] == mock_response
        
    def test_valid_list_response(self):
        """Test parsing valid list response."""
        mock_responses = [
            {
                "question_code": "M1021",
                "question_text": "Primary Diagnosis",
                "question_type": "text",
                "answer_context": "Patient has diabetes",
                "answer_reason": "Based on medical history",
                "answer_text": "Diabetes mellitus",
                "confidence_score": 0.95
            },
            {
                "question_code": "M1023",
                "question_text": "Secondary Diagnosis",
                "question_type": "text",
                "answer_context": "Patient has hypertension",
                "answer_reason": "Based on vital signs",
                "answer_text": "Hypertension",
                "confidence_score": 0.90
            }
        ]
        
        response_string = f"```json\n{json.dumps(mock_responses)}\n```"
        
        with patch('ai_core.answer_questions_langchain_knowledgebase_subgrouping.logger'):
            result = parse_and_validate_llm_response(response_string, self.question_answer_list)
            
        assert result is True
        assert len(self.question_answer_list) == 2
        assert self.question_answer_list == mock_responses
        
    def test_invalid_json(self):
        """Test handling of invalid JSON."""
        invalid_json = "```json\n{\"answer_test\":\"invalid json content\n```"
        
        with patch('ai_core.answer_questions_langchain_knowledgebase_subgrouping.logger') as mock_logger:
            with pytest.raises(json.JSONDecodeError):
                parse_and_validate_llm_response(invalid_json, self.question_answer_list)
                
        assert len(self.question_answer_list) == 0
        mock_logger.error.assert_called()
        
    def test_schema_validation_failure_single(self):
        """Test schema validation failure for single response."""
        # Missing required field 'question_code'
        invalid_response = {
            "question_text": "Primary Diagnosis",
            "question_type": "text",
            "answer_context": "Patient has diabetes",
            "answer_reason": "Based on medical history",
            "answer_text": "Diabetes mellitus",
            "confidence_score": 0.95
        }
        
        response_string = f"```json\n{json.dumps(invalid_response)}\n```"
        
        with patch('ai_core.answer_questions_langchain_knowledgebase_subgrouping.logger') as mock_logger:
            with pytest.raises(jsonschema.ValidationError):
                parse_and_validate_llm_response(response_string, self.question_answer_list)
                
        assert len(self.question_answer_list) == 0
        mock_logger.error.assert_called()
        
    def test_schema_validation_failure_list(self):
        """Test schema validation failure for list response."""
        # One item missing required field
        invalid_responses = [
            {
                "question_code": "M1021",
                "question_text": "Primary Diagnosis",
                "question_type": "text",
                "answer_context": "Patient has diabetes",
                "answer_reason": "Based on medical history",
                "answer_text": "Diabetes mellitus",
                "confidence_score": 0.95
            },
            {
                # Missing question_code
                "question_text": "Secondary Diagnosis",
                "question_type": "text",
                "answer_context": "Patient has hypertension",
                "answer_reason": "Based on vital signs",
                "answer_text": "Hypertension",
                "confidence_score": 0.90
            }
        ]
        
        response_string = f"```json\n{json.dumps(invalid_responses)}\n```"
        
        with patch('ai_core.answer_questions_langchain_knowledgebase_subgrouping.logger') as mock_logger:
            with pytest.raises(jsonschema.ValidationError):
                parse_and_validate_llm_response(response_string, self.question_answer_list)
                
        assert len(self.question_answer_list) == 0
        mock_logger.error.assert_called()
        
    def test_response_cleaning(self):
        """Test that response string is properly cleaned."""
        mock_response = {
            "question_code": "M1021",
            "question_text": "Primary Diagnosis",
            "question_type": "text",
            "answer_context": "Patient has diabetes",
            "answer_reason": "Based on medical history",
            "answer_text": "Diabetes mellitus",
            "confidence_score": 0.95
        }
        
        # Test various formats that need cleaning
        test_formats = [
            f"```json\n{json.dumps(mock_response)}\n```",
            f"```\n{json.dumps(mock_response)}\n```",
            f"json\n{json.dumps(mock_response)}",
            f"   {json.dumps(mock_response)}   ",  # whitespace
        ]
        
        for response_format in test_formats:
            self.question_answer_list.clear()
            
            with patch('ai_core.answer_questions_langchain_knowledgebase_subgrouping.logger'):
                result = parse_and_validate_llm_response(response_format, self.question_answer_list)
                
            assert result is True
            assert len(self.question_answer_list) == 1
            assert self.question_answer_list[0] == mock_response


if __name__ == "__main__":
    # Run the tests
    pytest.main([__file__, "-v"])
