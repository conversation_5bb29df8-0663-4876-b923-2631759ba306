import pytest
import os
import sys
from unittest.mock import Mock, patch
from datetime import datetime

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from ai_core.services.tenant_reports_service import (
    get_tenants,
    get_tenant_visits,
    get_visit_details,
    get_tenant_analytics,
    export_tenant_data,
    _parse_date_range,
    _build_visit_date_filter,
    _get_tenant_database
)

class TestTenantReportsService:
    """Test tenant reports service functionality."""
    
    def test_parse_date_range_valid_dates(self):
        """Test parsing valid date ranges."""
        start_dt, end_dt = _parse_date_range("2024-01-01", "2024-01-31")
        assert start_dt == datetime(2024, 1, 1)
        assert end_dt == datetime(2024, 1, 31, 23, 59, 59)
    
    def test_parse_date_range_invalid_dates(self):
        """Test parsing invalid date ranges."""
        with pytest.raises(ValueError, match="Invalid start_date format"):
            _parse_date_range("invalid-date", None)
        
        with pytest.raises(ValueError, match="Invalid end_date format"):
            _parse_date_range(None, "invalid-date")
    
    def test_parse_date_range_none_values(self):
        """Test parsing None date values."""
        start_dt, end_dt = _parse_date_range(None, None)
        assert start_dt is None
        assert end_dt is None
    
    def test_build_visit_date_filter(self):
        """Test building visit date filters."""
        start_dt = datetime(2024, 1, 1)
        end_dt = datetime(2024, 1, 31)
        
        # Test with both dates
        filter_result = _build_visit_date_filter(start_dt, end_dt)
        assert "visitDate" in filter_result
        assert "$gte" in filter_result["visitDate"]
        assert "$lte" in filter_result["visitDate"]
        assert filter_result["visitDate"]["$gte"] == "2024-01-01T00:00:00"
        assert filter_result["visitDate"]["$lte"] == "2024-01-31T23:59:59"
        
        # Test with start date only
        filter_result = _build_visit_date_filter(start_dt, None)
        assert "visitDate" in filter_result
        assert "$gte" in filter_result["visitDate"]
        assert "$lte" not in filter_result["visitDate"]
        
        # Test with end date only
        filter_result = _build_visit_date_filter(None, end_dt)
        assert "visitDate" in filter_result
        assert "$lte" in filter_result["visitDate"]
        assert "$gte" not in filter_result["visitDate"]
        
        # Test with no dates
        filter_result = _build_visit_date_filter(None, None)
        assert filter_result == {}
    
    @patch('ai_core.services.tenant_reports_service.MongoDBDatabase')
    def test_get_tenant_database(self, mock_mongodb):
        """Test getting tenant database connection."""
        # Mock admin database
        mock_admin_db = Mock()
        mock_admin_db.get_item.return_value = {
            "databaseName": "test_tenant_db"
        }
        
        # Mock MongoDB database
        mock_tenant_db = Mock()
        mock_mongodb.return_value = mock_tenant_db
        
        # Test getting tenant database
        result = _get_tenant_database("test_tenant_id", mock_admin_db)
        
        # Verify admin database was queried
        mock_admin_db.get_item.assert_called_once_with("tenants", "test_tenant_id")
        
        # Verify MongoDB database was created with correct parameters
        mock_mongodb.assert_called_once()
        call_args = mock_mongodb.call_args
        assert call_args[1]['database_name'] == "test_tenant_db"
    
    def test_get_tenants(self):
        """Test getting list of tenants."""
        # Mock database
        mock_db = Mock()
        mock_db.get_all_items.return_value = [
            {
                "_id": "tenant1",
                "tenantName": "Test Tenant 1",
                "uniqueName": "test1",
                "databaseName": "test1_db",
                "createdAt": "2024-01-01T00:00:00Z",
                "updatedAt": "2024-01-01T00:00:00Z"
            },
            {
                "_id": "tenant2",
                "tenantName": "Test Tenant 2",
                "uniqueName": "test2",
                "databaseName": "test2_db",
                "createdAt": "2024-01-02T00:00:00Z",
                "updatedAt": "2024-01-02T00:00:00Z"
            }
        ]
        
        # Mock user and request
        mock_user = {"id": "user1", "role": "admin"}
        mock_request = Mock()
        
        # Test getting tenants
        result = get_tenants(mock_db, mock_user, mock_request)
        
        # Verify database was queried
        mock_db.get_all_items.assert_called_once_with("tenants")
        
        # Verify result format
        assert len(result) == 2
        assert result[0]["_id"] == "tenant1"
        assert result[0]["tenantName"] == "Test Tenant 1"
        assert result[1]["_id"] == "tenant2"
        assert result[1]["tenantName"] == "Test Tenant 2"
    
    @patch('ai_core.services.tenant_reports_service._get_tenant_database')
    def test_get_tenant_visits(self, mock_get_tenant_db):
        """Test getting tenant visits."""
        # Mock tenant database
        mock_tenant_db = Mock()
        mock_tenant_db.query_items.return_value = [
            {
                "_id": "visit1",
                "no": "V123456",
                "visitDate": "2024-01-15T00:00:00.000Z",
                "startTime": "09:00 AM",
                "type": "SOC",
                "status": "Completed"
            },
            {
                "_id": "visit2",
                "no": "V123457",
                "visitDate": "2024-01-16T00:00:00.000Z",
                "startTime": "10:00 AM",
                "type": "ROC",
                "status": "In Progress"
            }
        ]
        mock_get_tenant_db.return_value = mock_tenant_db
        
        # Mock admin database
        mock_admin_db = Mock()
        mock_admin_db.get_item.return_value = {
            "_id": "tenant1",
            "tenantName": "Test Tenant",
            "uniqueName": "test",
            "databaseName": "test_db",
            "createdAt": "2024-01-01T00:00:00Z",
            "updatedAt": "2024-01-01T00:00:00Z"
        }
        
        # Mock user and request
        mock_user = {"id": "user1", "role": "admin"}
        mock_request = Mock()
        
        # Test getting tenant visits
        result = get_tenant_visits(
            "tenant1",
            mock_admin_db,
            mock_user,
            mock_request,
            status="Completed",
            visit_type="SOC",
            start_date="2024-01-01",
            end_date="2024-01-31",
            limit=50,
            offset=0
        )
        
        # Verify tenant database was queried
        mock_tenant_db.query_items.assert_called_once()
        
        # Verify result format
        assert "tenant" in result
        assert "visits" in result
        assert "total_visits" in result
        assert "visits_by_status" in result
        assert "visits_by_type" in result
        assert result["tenant"]["tenantName"] == "Test Tenant"
        assert len(result["visits"]) == 2
    
    @patch('ai_core.services.tenant_reports_service._get_tenant_database')
    def test_get_visit_details(self, mock_get_tenant_db):
        """Test getting visit details."""
        # Mock tenant database
        mock_tenant_db = Mock()
        mock_tenant_db.get_item.side_effect = lambda collection, id: {
            "visits": {
                "_id": "visit1",
                "no": "V123456",
                "visitDate": "2024-01-15T00:00:00.000Z",
                "startTime": "09:00 AM",
                "type": "SOC",
                "status": "Completed",
                "clientId": "client1",
                "episodeId": "episode1",
                "clinicianId": "clinician1"
            },
            "clients": {
                "_id": "client1",
                "name": "John Doe"
            },
            "clinician_infos": {
                "_id": "clinician1",
                "name": "Dr. Smith"
            }
        }.get(collection, {})
        
        mock_get_tenant_db.return_value = mock_tenant_db
        
        # Mock admin database
        mock_admin_db = Mock()
        
        # Mock user and request
        mock_user = {"id": "user1", "role": "admin"}
        mock_request = Mock()
        
        # Test getting visit details
        result = get_visit_details("tenant1", "visit1", mock_admin_db, mock_user, mock_request)
        
        # Verify result format
        assert "visit" in result
        assert "related_data" in result
        assert result["visit"]["no"] == "V123456"
        assert result["related_data"]["client"]["name"] == "John Doe"
        assert result["related_data"]["clinician"]["name"] == "Dr. Smith"

if __name__ == "__main__":
    pytest.main([__file__])
