"""
Unit tests for M-GG inconsistency flagging functionality
"""

import unittest
import json
import tempfile
import os
import re


def flag_m_gg_inconsistencies(responses_data, flagging_rules_path=None):
    """
    Flag inconsistencies between M and GG questions based on defined rules.

    Args:
        responses_data: List of response objects with question_code and answer_text
        flagging_rules_path: Optional path to the JSON file containing flagging rules.
                            If None, uses flagging_rules_m_gg.json from the same directory.

    Returns:
        Dict: {rule_name: [discrepancy_objects]} where each discrepancy object contains:
              {M_Item, GG_item, isDiscrepant, notes}
    """

    # Determine the rules file path
    if flagging_rules_path is None:
        # For testing, we'll use the actual rules file location
        flagging_rules_path = "apps/ai_core/src/ai_core/services/flagging_rules_m_gg.json"

    # Load flagging rules
    try:
        with open(flagging_rules_path, 'r') as f:
            flagging_rules = json.load(f)
    except FileNotFoundError:
        return {"error": f"Flagging rules file not found: {flagging_rules_path}"}

    # Create a lookup dictionary for responses by question_code
    response_lookup = {}
    for response in responses_data:
        question_code = response.get("question_code", "").upper()
        response_lookup[question_code] = response

    def extract_numeric_value(answer_text):
        """Extract numeric value from answer text (e.g., '06 - Independent' -> 6)"""
        if not answer_text or not isinstance(answer_text, list) or len(answer_text) == 0:
            return None

        text = str(answer_text[0]) if isinstance(answer_text, list) else str(answer_text)
        # Extract first number from the text
        match = re.match(r'^(\d+)', text.strip())
        if match:
            return int(match.group(1))
        return None

    # Process each rule set
    flagging_results = {}

    for rule_name, rule_data in flagging_rules.items():
        discrepancies = []
        rules = rule_data.get("rules", {})
        question_pairs = rule_data.get("question_pairs", [])

        for pair in question_pairs:
            m_item = pair["M_item"].upper()
            gg_item = pair["GG_item"].upper()
            pair_notes = pair.get("notes", "")

            # Get M item response
            m_response = response_lookup.get(m_item)
            if not m_response:
                discrepancies.append({
                    "M_Item": m_item,
                    "GG_item": gg_item,
                    "isDiscrepant": True,
                    "notes": f"M item {m_item} not found in responses. {pair_notes}"
                })
                continue

            # Get GG item response
            gg_response = response_lookup.get(gg_item)
            if not gg_response:
                discrepancies.append({
                    "M_Item": m_item,
                    "GG_item": gg_item,
                    "isDiscrepant": True,
                    "notes": f"GG item {gg_item} not found in responses. {pair_notes}"
                })
                continue

            # Extract numeric values
            m_value = extract_numeric_value(m_response.get("answer_text"))
            gg_value = extract_numeric_value(gg_response.get("answer_text"))

            if m_value is None:
                discrepancies.append({
                    "M_Item": m_item,
                    "GG_item": gg_item,
                    "isDiscrepant": True,
                    "notes": f"Could not extract numeric value from M item {m_item}. {pair_notes}"
                })
                continue

            if gg_value is None:
                discrepancies.append({
                    "M_Item": m_item,
                    "GG_item": gg_item,
                    "isDiscrepant": True,
                    "notes": f"Could not extract numeric value from GG item {gg_item}. {pair_notes}"
                })
                continue

            # Check rule compliance
            m_value_str = str(m_value)
            allowed_gg_values = rules.get(m_value_str, [])

            is_discrepant = gg_value not in allowed_gg_values

            if is_discrepant:
                discrepancies.append({
                    "M_Item": m_item,
                    "GG_item": gg_item,
                    "isDiscrepant": True,
                    "notes": f"M={m_value}, GG={gg_value}, Expected GG in {allowed_gg_values}. {pair_notes}"
                })
            else:
                # Also include non-discrepant pairs for completeness
                discrepancies.append({
                    "M_Item": m_item,
                    "GG_item": gg_item,
                    "isDiscrepant": False,
                    "notes": f"M={m_value}, GG={gg_value}, Compliant. {pair_notes}"
                })

        flagging_results[rule_name] = discrepancies

    return flagging_results


class TestFlaggingMGG(unittest.TestCase):

    def setUp(self):
        """Set up test fixtures with ACTUAL rules from the real file"""
        # Use the actual rules from your flagging_rules_m_gg.json
        self.actual_rules = {
            "7_rule_partial_independence": {
                "rules": {
                    "0": [6],
                    "1": [5],
                    "2": [2,3,4],
                    "3": [2,3,4],
                    "4": [1],
                    "5": [1],
                    "6": [1]
                },
                "question_pairs": [
                    {"M_item": "M1810", "GG_item": "GG0130.A", "notes": "Grooming ≈ oral hygiene"},
                    {"M_item": "M1810", "GG_item": "GG0130.B", "notes": "Grooming ≈ oral hygiene"},
                    {"M_item": "M1820", "GG_item": "GG0130.C", "notes": "Upper dressing"},
                    {"M_item": "M1830", "GG_item": "GG0130.E", "notes": "Bathing"},
                    {"M_item": "M1840", "GG_item": "GG0130.C", "notes": "Toileting transfer"},
                    {"M_item": "M1840", "GG_item": "GG0170.D", "notes": "Toileting transfer"},
                    {"M_item": "M1845", "GG_item": "GG0130.C", "notes": "Toileting hygiene"},
                    {"M_item": "M1850", "GG_item": "GG0170.B", "notes": "Bed/chair transfers"},
                    {"M_item": "M1850", "GG_item": "GG0170.C", "notes": "Bed/chair transfers"},
                    {"M_item": "M1860", "GG_item": "GG0170.J", "notes": "Ambulation/locomotion"},
                    {"M_item": "M1860", "GG_item": "GG0170.K", "notes": "Ambulation/locomotion"},
                    {"M_item": "M1860", "GG_item": "GG0170.P", "notes": "Ambulation/locomotion"}
                ],
                "notes": "Applies to M1800–M1870 with GG0130–GG0170"
            }
        }

        # Create temporary file with actual rules
        self.temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
        json.dump(self.actual_rules, self.temp_file)
        self.temp_file.close()

        # All unique M items from the rules
        self.all_m_items = ["M1810", "M1820", "M1830", "M1840", "M1845", "M1850", "M1860"]

        # All unique GG items from the rules
        self.all_gg_items = ["GG0130.A", "GG0130.B", "GG0130.C", "GG0130.E",
                            "GG0170.B", "GG0170.C", "GG0170.D", "GG0170.J",
                            "GG0170.K", "GG0170.P"]

    def tearDown(self):
        """Clean up test fixtures"""
        os.unlink(self.temp_file.name)
    
    def test_all_rule_values_compliant(self):
        """Test ALL 7 rule values (0,1,2,3,4,5,6) with compliant GG values"""
        # Test each rule: M value -> allowed GG values
        test_cases = [
            ("0", "6"),  # M=0 should have GG=6
            ("1", "5"),  # M=1 should have GG=5
            ("2", "2"),  # M=2 should have GG in [2,3,4] - test with 2
            ("2", "3"),  # M=2 should have GG in [2,3,4] - test with 3
            ("2", "4"),  # M=2 should have GG in [2,3,4] - test with 4
            ("3", "2"),  # M=3 should have GG in [2,3,4] - test with 2
            ("3", "3"),  # M=3 should have GG in [2,3,4] - test with 3
            ("3", "4"),  # M=3 should have GG in [2,3,4] - test with 4
            ("4", "1"),  # M=4 should have GG=1
            ("5", "1"),  # M=5 should have GG=1
            ("6", "1"),  # M=6 should have GG=1
        ]

        for m_val, gg_val in test_cases:
            with self.subTest(m_value=m_val, gg_value=gg_val):
                responses = [
                    {"question_code": "M1810", "answer_text": [f"{m_val} - Test"]},
                    {"question_code": "GG0130.A", "answer_text": [f"{gg_val.zfill(2)} - Test"]}
                ]

                results = flag_m_gg_inconsistencies(responses, self.temp_file.name)
                discrepancies = results["7_rule_partial_independence"]

                # Find the M1810-GG0130.A pair
                pair_result = next(d for d in discrepancies if d["M_Item"] == "M1810" and d["GG_item"] == "GG0130.A")
                self.assertFalse(pair_result["isDiscrepant"],
                               f"M={m_val}, GG={gg_val} should be compliant but was flagged as discrepant")
        
    def test_all_rule_values_discrepant(self):
        """Test ALL 7 rule values with WRONG GG values to ensure discrepancy detection"""
        # Test each rule with wrong GG values
        test_cases = [
            ("0", "1", [6]),    # M=0 with GG=1, should be GG=6
            ("0", "5", [6]),    # M=0 with GG=5, should be GG=6
            ("1", "6", [5]),    # M=1 with GG=6, should be GG=5
            ("1", "2", [5]),    # M=1 with GG=2, should be GG=5
            ("2", "1", [2,3,4]), # M=2 with GG=1, should be GG in [2,3,4]
            ("2", "6", [2,3,4]), # M=2 with GG=6, should be GG in [2,3,4]
            ("3", "1", [2,3,4]), # M=3 with GG=1, should be GG in [2,3,4]
            ("3", "6", [2,3,4]), # M=3 with GG=6, should be GG in [2,3,4]
            ("4", "2", [1]),    # M=4 with GG=2, should be GG=1
            ("4", "6", [1]),    # M=4 with GG=6, should be GG=1
            ("5", "3", [1]),    # M=5 with GG=3, should be GG=1
            ("5", "6", [1]),    # M=5 with GG=6, should be GG=1
            ("6", "4", [1]),    # M=6 with GG=4, should be GG=1
            ("6", "6", [1]),    # M=6 with GG=6, should be GG=1
        ]

        for m_val, gg_val, expected_gg in test_cases:
            with self.subTest(m_value=m_val, gg_value=gg_val):
                responses = [
                    {"question_code": "M1810", "answer_text": [f"{m_val} - Test"]},
                    {"question_code": "GG0130.A", "answer_text": [f"{gg_val.zfill(2)} - Test"]}
                ]

                results = flag_m_gg_inconsistencies(responses, self.temp_file.name)
                discrepancies = results["7_rule_partial_independence"]

                # Find the M1810-GG0130.A pair
                pair_result = next(d for d in discrepancies if d["M_Item"] == "M1810" and d["GG_item"] == "GG0130.A")
                self.assertTrue(pair_result["isDiscrepant"],
                              f"M={m_val}, GG={gg_val} should be discrepant but was marked compliant")
                self.assertIn(f"Expected GG in {expected_gg}", pair_result["notes"])
        
    def test_all_question_pairs_coverage(self):
        """Test that ALL 12 question pairs are processed"""
        # Create responses for all M and GG items with compliant values
        responses = []

        # Add all M items with value 0 (should map to GG=6)
        for m_item in self.all_m_items:
            responses.append({"question_code": m_item, "answer_text": ["0 - Independent"]})

        # Add all GG items with value 6 (compliant with M=0)
        for gg_item in self.all_gg_items:
            responses.append({"question_code": gg_item, "answer_text": ["06 - Independent"]})

        results = flag_m_gg_inconsistencies(responses, self.temp_file.name)
        discrepancies = results["7_rule_partial_independence"]

        # Should have exactly 12 pairs (as defined in the rules)
        self.assertEqual(len(discrepancies), 12, "Should process all 12 question pairs")

        # All should be compliant since M=0 maps to GG=6
        compliant_count = sum(1 for d in discrepancies if not d["isDiscrepant"])
        self.assertEqual(compliant_count, 12, "All pairs should be compliant with M=0, GG=6")

        # Verify all expected pairs are present
        expected_pairs = [
            ("M1810", "GG0130.A"), ("M1810", "GG0130.B"), ("M1820", "GG0130.C"),
            ("M1830", "GG0130.E"), ("M1840", "GG0130.C"), ("M1840", "GG0170.D"),
            ("M1845", "GG0130.C"), ("M1850", "GG0170.B"), ("M1850", "GG0170.C"),
            ("M1860", "GG0170.J"), ("M1860", "GG0170.K"), ("M1860", "GG0170.P")
        ]

        actual_pairs = [(d["M_Item"], d["GG_item"]) for d in discrepancies]
        for expected_pair in expected_pairs:
            self.assertIn(expected_pair, actual_pairs, f"Missing expected pair: {expected_pair}")

    def test_missing_m_items(self):
        """Test when M items are missing from responses"""
        # Only provide GG items, no M items
        responses = [{"question_code": gg_item, "answer_text": ["06 - Independent"]}
                    for gg_item in self.all_gg_items]

        results = flag_m_gg_inconsistencies(responses, self.temp_file.name)
        discrepancies = results["7_rule_partial_independence"]

        # All 12 pairs should be discrepant due to missing M items
        self.assertEqual(len(discrepancies), 12)
        discrepant_count = sum(1 for d in discrepancies if d["isDiscrepant"])
        self.assertEqual(discrepant_count, 12, "All pairs should be discrepant due to missing M items")

        # Check that notes mention missing M items
        for discrepancy in discrepancies:
            self.assertIn("not found in responses", discrepancy["notes"])
            self.assertIn("M item", discrepancy["notes"])
        
    def test_missing_gg_items(self):
        """Test when GG items are missing from responses"""
        # Only provide M items, no GG items
        responses = [{"question_code": m_item, "answer_text": ["0 - Independent"]}
                    for m_item in self.all_m_items]

        results = flag_m_gg_inconsistencies(responses, self.temp_file.name)
        discrepancies = results["7_rule_partial_independence"]

        # All 12 pairs should be discrepant due to missing GG items
        self.assertEqual(len(discrepancies), 12)
        discrepant_count = sum(1 for d in discrepancies if d["isDiscrepant"])
        self.assertEqual(discrepant_count, 12, "All pairs should be discrepant due to missing GG items")

        # Check that notes mention missing GG items
        for discrepancy in discrepancies:
            self.assertIn("not found in responses", discrepancy["notes"])
            self.assertIn("GG item", discrepancy["notes"])
        
    def test_invalid_answer_formats(self):
        """Test various invalid answer formats that cannot be parsed"""
        test_cases = [
            # Invalid M item formats
            (["Not a number - Invalid"], ["06 - Independent"], "Could not extract numeric value from M item"),
            ([""], ["06 - Independent"], "Could not extract numeric value from M item"),
            (["   - No number"], ["06 - Independent"], "Could not extract numeric value from M item"),
            ([], ["06 - Independent"], "Could not extract numeric value from M item"),

            # Invalid GG item formats
            (["0 - Independent"], ["Not a number - Invalid"], "Could not extract numeric value from GG item"),
            (["0 - Independent"], [""], "Could not extract numeric value from GG item"),
            (["0 - Independent"], ["   - No number"], "Could not extract numeric value from GG item"),
            (["0 - Independent"], [], "Could not extract numeric value from GG item"),
        ]

        for m_answer, gg_answer, expected_error in test_cases:
            with self.subTest(m_answer=m_answer, gg_answer=gg_answer):
                responses = [
                    {"question_code": "M1810", "answer_text": m_answer},
                    {"question_code": "GG0130.A", "answer_text": gg_answer}
                ]

                results = flag_m_gg_inconsistencies(responses, self.temp_file.name)
                discrepancies = results["7_rule_partial_independence"]

                # Find the M1810-GG0130.A pair
                pair_result = next(d for d in discrepancies if d["M_Item"] == "M1810" and d["GG_item"] == "GG0130.A")
                self.assertTrue(pair_result["isDiscrepant"])
                self.assertIn(expected_error, pair_result["notes"])
        
    def test_missing_rules_file(self):
        """Test when rules file doesn't exist"""
        responses = []
        results = flag_m_gg_inconsistencies(responses, "nonexistent_file.json")

        self.assertIn("error", results)
        self.assertIn("Flagging rules file not found", results["error"])
        self.assertIn("nonexistent_file.json", results["error"])

    def test_numeric_extraction_edge_cases(self):
        """Test numeric value extraction from various answer formats"""
        test_cases = [
            # Standard formats
            ("0 - Independent", 0),
            ("06 - Independent", 6),
            ("10 - Dependent", 10),
            ("1 - Minimal assistance", 1),
            ("02 - Substantial assistance", 2),

            # Edge cases with leading/trailing spaces
            ("  3 - Test", 3),
            ("4 - Test  ", 4),
            ("  05 - Test  ", 5),

            # Numbers in different positions (should extract first number)
            ("7 - Has 2 parts", 7),
            ("8 - Test with 10 items", 8),
        ]

        for answer_text, expected_value in test_cases:
            with self.subTest(answer_text=answer_text):
                responses = [
                    {"question_code": "M1810", "answer_text": [answer_text]},
                    {"question_code": "GG0130.A", "answer_text": ["06 - Independent"]}  # Always compliant with M=0
                ]

                # Adjust M value to make it compliant for testing extraction
                if expected_value == 0:
                    responses[1]["answer_text"] = ["06 - Independent"]  # M=0 -> GG=6
                elif expected_value == 1:
                    responses[1]["answer_text"] = ["05 - Independent"]  # M=1 -> GG=5
                else:
                    responses[1]["answer_text"] = [f"{expected_value:02d} - Independent"]  # M=2,3 -> GG=2,3,4

                results = flag_m_gg_inconsistencies(responses, self.temp_file.name)
                discrepancies = results["7_rule_partial_independence"]

                # Find the M1810-GG0130.A pair
                pair_result = next(d for d in discrepancies if d["M_Item"] == "M1810" and d["GG_item"] == "GG0130.A")
                self.assertIn(f"M={expected_value}", pair_result["notes"],
                            f"Failed to extract {expected_value} from '{answer_text}'")

    def test_case_insensitive_question_codes(self):
        """Test that question codes are handled case-insensitively"""
        responses = [
            {"question_code": "m1810", "answer_text": ["0 - Independent"]},  # lowercase
            {"question_code": "gg0130.a", "answer_text": ["06 - Independent"]},  # lowercase
        ]

        results = flag_m_gg_inconsistencies(responses, self.temp_file.name)
        discrepancies = results["7_rule_partial_independence"]

        # Should find the pair despite case differences
        pair_result = next(d for d in discrepancies if d["M_Item"] == "M1810" and d["GG_item"] == "GG0130.A")
        self.assertFalse(pair_result["isDiscrepant"], "Case insensitive matching should work")

    def test_edge_case_rule_values_not_in_rules(self):
        """Test M values that are not defined in the rules"""
        responses = [
            {"question_code": "M1810", "answer_text": ["99 - Unknown value"]},  # M=99 not in rules
            {"question_code": "GG0130.A", "answer_text": ["06 - Independent"]},
        ]

        results = flag_m_gg_inconsistencies(responses, self.temp_file.name)
        discrepancies = results["7_rule_partial_independence"]

        # Should be discrepant because M=99 is not in rules (no allowed GG values)
        pair_result = next(d for d in discrepancies if d["M_Item"] == "M1810" and d["GG_item"] == "GG0130.A")
        self.assertTrue(pair_result["isDiscrepant"])
        self.assertIn("Expected GG in []", pair_result["notes"])  # Empty array for undefined M values


if __name__ == '__main__':
    unittest.main()
