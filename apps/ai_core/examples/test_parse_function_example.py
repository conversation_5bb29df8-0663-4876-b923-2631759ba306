"""
Example script demonstrating how to test the parse_and_validate_llm_response function
with mock responses and exception scenarios.

This script shows practical usage of the extracted function for testing purposes.
"""

import json
import sys
import os
from unittest.mock import patch

# Add the src directory to the path so we can import the module
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, '..', 'src')
sys.path.insert(0, src_dir)

try:
    from ai_core.answer_questions_langchain_knowledgebase_subgrouping import parse_and_validate_llm_response
except ImportError:
    # Try alternative import
    try:
        sys.path.insert(0, os.path.join(current_dir, '..'))
        from src.ai_core.answer_questions_langchain_knowledgebase_subgrouping import parse_and_validate_llm_response
    except ImportError:
        # Last resort - direct file import
        import importlib.util
        module_path = os.path.join(src_dir, 'ai_core', 'answer_questions_langchain_knowledgebase_subgrouping.py')
        spec = importlib.util.spec_from_file_location("answer_questions_module", module_path)
        answer_questions_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(answer_questions_module)
        parse_and_validate_llm_response = answer_questions_module.parse_and_validate_llm_response


def test_successful_parsing():
    """Test successful parsing scenarios."""
    print("=== Testing Successful Parsing ===")
    
    # Test data
    question_answer_list = []
    
    # Mock single response
    single_response = {
        "question_code": "M1021",
        "question_text": "Primary Diagnosis",
        "question_type": "text",
        "answer_context": "Patient has diabetes mellitus type 2",
        "answer_reason": "Based on medical history and current medications",
        "answer_text": "Diabetes mellitus, type 2",
        "confidence_score": 0.95
    }
    
    # Mock list response
    list_response = [
        {
            "question_code": "M1021",
            "question_text": "Primary Diagnosis",
            "question_type": "text",
            "answer_context": "Patient has diabetes mellitus type 2",
            "answer_reason": "Based on medical history and current medications",
            "answer_text": "Diabetes mellitus, type 2",
            "confidence_score": 0.95
        },
        {
            "question_code": "M1023",
            "question_text": "Secondary Diagnosis",
            "question_type": "text",
            "answer_context": "Patient has hypertension",
            "answer_reason": "Based on vital signs and medication list",
            "answer_text": "Essential hypertension",
            "confidence_score": 0.90
        }
    ]
    
    # Test single response
    print("1. Testing single response...")
    response_string = f"```json\n{json.dumps(single_response)}\n```"
    
    with patch('ai_core.answer_questions_langchain_knowledgebase_subgrouping.logger'):
        result = parse_and_validate_llm_response(response_string, question_answer_list)
        
    print(f"   Result: {result}")
    print(f"   Items added: {len(question_answer_list)}")
    print(f"   First item: {question_answer_list[0]['question_code'] if question_answer_list else 'None'}")
    
    # Test list response
    print("\n2. Testing list response...")
    question_answer_list.clear()
    response_string = f"```json\n{json.dumps(list_response)}\n```"
    
    with patch('ai_core.answer_questions_langchain_knowledgebase_subgrouping.logger'):
        result = parse_and_validate_llm_response(response_string, question_answer_list)
        
    print(f"   Result: {result}")
    print(f"   Items added: {len(question_answer_list)}")
    print(f"   Question codes: {[item['question_code'] for item in question_answer_list]}")


def test_exception_scenarios():
    """Test various exception scenarios."""
    print("\n=== Testing Exception Scenarios ===")
    
    question_answer_list = []
    
    # Test 1: Empty response
    print("1. Testing empty response...")
    try:
        with patch('ai_core.answer_questions_langchain_knowledgebase_subgrouping.logger'):
            result = parse_and_validate_llm_response("", question_answer_list)
        print(f"   Result: {result} (Expected: False)")
    except Exception as e:
        print(f"   Exception: {e}")
    
    # Test 2: Invalid JSON
    print("\n2. Testing invalid JSON...")
    try:
        with patch('ai_core.answer_questions_langchain_knowledgebase_subgrouping.logger'):
            result = parse_and_validate_llm_response("```json\n{invalid json\n```", question_answer_list)
        print(f"   Result: {result}")
    except Exception as e:
        print(f"   Exception caught: {type(e).__name__}: {e}")
    
    # Test 3: Missing required field
    print("\n3. Testing schema validation failure...")
    invalid_response = {
        # Missing question_code
        "question_text": "Primary Diagnosis",
        "question_type": "text",
        "answer_context": "Patient has diabetes",
        "answer_reason": "Based on medical history",
        "answer_text": "Diabetes mellitus",
        "confidence_score": 0.95
    }
    
    try:
        response_string = f"```json\n{json.dumps(invalid_response)}\n```"
        with patch('ai_core.answer_questions_langchain_knowledgebase_subgrouping.logger'):
            result = parse_and_validate_llm_response(response_string, question_answer_list)
        print(f"   Result: {result}")
    except Exception as e:
        print(f"   Exception caught: {type(e).__name__}: {e}")
    
    # Test 4: Wrong data type
    print("\n4. Testing wrong data type...")
    try:
        response_string = f"```json\n{json.dumps('just a string')}\n```"
        with patch('ai_core.answer_questions_langchain_knowledgebase_subgrouping.logger'):
            result = parse_and_validate_llm_response(response_string, question_answer_list)
        print(f"   Result: {result}")
    except Exception as e:
        print(f"   Exception caught: {type(e).__name__}: {e}")


def test_response_cleaning():
    """Test response string cleaning functionality."""
    print("\n=== Testing Response Cleaning ===")
    
    question_answer_list = []
    
    # Valid response data
    response_data = {
        "question_code": "M1021",
        "question_text": "Primary Diagnosis",
        "question_type": "text",
        "answer_context": "Patient has diabetes",
        "answer_reason": "Based on medical history",
        "answer_text": "Diabetes mellitus",
        "confidence_score": 0.95
    }
    
    # Test different formats that need cleaning
    test_formats = [
        ("Standard format", f"```json\n{json.dumps(response_data)}\n```"),
        ("No json keyword", f"```\n{json.dumps(response_data)}\n```"),
        ("Only json keyword", f"json\n{json.dumps(response_data)}"),
        ("With whitespace", f"   {json.dumps(response_data)}   "),
        ("Mixed format", f"```json{json.dumps(response_data)}```"),
    ]
    
    for format_name, response_string in test_formats:
        print(f"\n{format_name}:")
        question_answer_list.clear()
        
        try:
            with patch('ai_core.answer_questions_langchain_knowledgebase_subgrouping.logger'):
                result = parse_and_validate_llm_response(response_string, question_answer_list)
            print(f"   Result: {result}")
            print(f"   Items parsed: {len(question_answer_list)}")
        except Exception as e:
            print(f"   Exception: {type(e).__name__}: {e}")


def main():
    """Run all test scenarios."""
    print("Testing parse_and_validate_llm_response function")
    print("=" * 50)
    
    test_successful_parsing()
    test_exception_scenarios()
    test_response_cleaning()
    
    print("\n" + "=" * 50)
    print("Testing completed!")


if __name__ == "__main__":
    main()
